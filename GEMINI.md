This is a multi-module Maven project. The parent `pom.xml` defines the overall project structure and dependencies.

**Key Technologies:**

*   **Java:** Version 21
*   **Spring Boot:** Version 3.3.2
*   **Spring Cloud:** Version 2023.0.3
*   **Spring Modulith:** Version 1.2.1
*   **Maven:** Used for dependency management and build automation.

**Modules:**

*   `ahss-gateway`: API Gateway for the system.
*   `ahss-common`: Common utilities and components shared across modules.
*   `ahss-product-portal`: Product portal module.
*   `ahss-uam`: User Access Management module.

**Build Process:**

*   The project is built using Maven.
*   The `mvnw` and `mvnw.cmd` scripts are the Maven wrappers, which allow building the project without installing <PERSON><PERSON> locally.
*   The `pom.xml` file defines the build process, including plugins for testing (Surefire) and code coverage (JaCoCo).
*   There is a profile `no-tests` to skip tests during the build.

**CI/CD:**

*   The `.azure` directory contains Azure Pipelines configurations, suggesting that the project is deployed using Azure DevOps.
*   The pipeline includes steps for building, scanning, and deploying the application.

**Docker:**

*   The `docker` directory contains Docker Compose files for running the application and its dependencies (like <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> for tracing) in a containerized environment.

**Helm:**

*   The `helm` directory contains Helm charts for deploying the application to Kubernetes.

**Scripts:**

*   The `scripts` directory contains a `pre-push` hook and a script to run all services.

**AGIC Configuration:**

The Application Gateway Ingress Controller (AGIC) is configured via Helm charts. The configuration can be found in the `values-dev.yaml` file for the `ahss-gateway-web` service.

*   **Ingress Class:** `azure-application-gateway`
*   **Annotations:**
    *   `appgw.ingress.kubernetes.io/backend-path-prefix: "/"`
    *   `appgw.ingress.kubernetes.io/use-private-ip: "true"`
    *   `appgw.ingress.kubernetes.io/health-probe-port: "8888"`
    *   `appgw.ingress.kubernetes.io/health-probe-path: "/actuator/health"`
*   **Host:** `dev.api.ahpp.amplifyhealth.com`
*   **Path:** `/ahss-gateway/*`
*   **TLS:** Enabled, with the certificate stored in the `devapiahpp-tls` secret.

---

### Adding Multiple Domain Listeners and Path Routing

To add multiple domain listeners and path routings with proper TLS secrets per domain, you need to update the `ingress.main` section in your environment-specific `values-*.yaml` file (e.g., `values-dev.yaml`).

1.  **Update the `hosts` list:** Add a new entry to the `ingress.main.hosts` list for each new domain. Each entry will contain the hostname and the paths you want to route for that host.

2.  **Update the `tls` list:** Add a corresponding entry to the `ingress.main.tls` list for each new domain. This entry will specify the hostname and the name of the Kubernetes secret that contains the TLS certificate for that domain.

Here's an example of how the updated `ingress` section would look:

```yaml
ingress:
  main:
    enabled: true
    className: azure-application-gateway
    annotations:
      appgw.ingress.kubernetes.io/backend-path-prefix: "/"
      appgw.ingress.kubernetes.io/use-private-ip: "true"
      appgw.ingress.kubernetes.io/health-probe-port: "8888"
      appgw.ingress.kubernetes.io/health-probe-path: "/actuator/health"
    hosts:
      - host: dev.api.ahpp.amplifyhealth.com
        paths:
          - path: /ahss-gateway/*
            pathType: Prefix
            service:
              identifier: main
      - host: dev.api.another-domain.com
        paths:
          - path: /some-other-service/*
            pathType: Prefix
            service:
              # You can route to the same service or a different one
              identifier: main 
    tls:
      - hosts:
          - dev.api.ahpp.amplifyhealth.com
        secretName: devapiahpp-tls
      - hosts:
          - dev.api.another-domain.com
        secretName: devapianotherdomain-tls
```

#### Creating the TLS Secret

You'll also need to create the new TLS secret in your Kubernetes cluster. You can do this using the following `kubectl` command:

```bash
kubectl create secret tls devapianotherdomain-tls --key /path/to/your/key.pem --cert /path/to/your/cert.pem
```

Replace `/path/to/your/key.pem` and `/path/to/your/cert.pem` with the actual paths to your TLS key and certificate files for the new domain.
