# Amplify Health Shared Services

## Description

This project contains multiple microservices for Amplify Health, built with Spring Boot 3.5.4 and Java 21.

## Services

- [Amplify Health Shared Services Gateway](./ahss-gateway/AHSSGW.md)
- [Amplify Health Product Portal](./ahss-product-portal/AHPP.md)
- [Amplify Health UAM](./ahss-uam/UAM.md)

## Project Structure

This project consists of several microservices:

- **ahss-gateway**: API Gateway service that routes requests to appropriate microservices
- **ahss-product-portal**: Product Portal service for managing products and related operations
- **ahss-uam**: User Access Management service for authentication and authorization
- **ahss-common**: Shared utilities and common components used across all services

## Module Dependencies Architecture

### Dependency Hierarchy

The project follows a strict dependency hierarchy to avoid circular dependencies:

```mermaid
graph TD
    %% Application Layer (Interface)
    subgraph AL["🌐 APPLICATION LAYER"]
        GW_WEB["ahss-gateway-web<br/>(Interface)"]
        PP_WEB["ahss-pp-web<br/>(Interface)"]
        UAM_WEB["ahss-uam-web<br/>(Interface)"]
    end

    %% Business Layer (Components)
    subgraph BL["⚙️ BUSINESS LAYER"]
        subgraph GW_COMP["Gateway Components"]
            GW_CORE["ahss-gateway-core"]
            GW_CONFIG["ahss-gateway-config"]
            GW_DOMAIN["ahss-gateway-domain"]
            GW_DB["ahss-gateway-db"]
            GW_COMMON["ahss-gateway-common"]
        end
        
        subgraph PP_COMP["Product Portal Components"]
            PP_CORE["ahss-pp-core"]
            PP_CONFIG["ahss-pp-config"]
            PP_DOMAIN["ahss-pp-domain"]
            PP_DB["ahss-pp-db"]
            PP_COMMON["ahss-pp-common"]
        end
        
        subgraph UAM_COMP["UAM Components"]
            UAM_CORE["ahss-uam-core"]
            UAM_CONFIG["ahss-uam-config"]
            UAM_DOMAIN["ahss-uam-domain"]
            UAM_DB["ahss-uam-db"]
            UAM_COMMON["ahss-uam-common"]
        end
    end

    %% Shared Layer
    subgraph SL["🔧 SHARED LAYER"]
        subgraph COMMON["ahss-common"]
            COMMON_API["common-api-response<br/>(API utilities)"]
            COMMON_CONFIG["common-config<br/>(Configuration)"]
            COMMON_DOMAIN["common-domain<br/>(Domain models)"]
            COMMON_UTILS["common-utils<br/>(Utilities)"]
        end
    end

    %% Interface to Business Dependencies
    GW_WEB --> GW_COMP
    PP_WEB --> PP_COMP
    UAM_WEB --> UAM_COMP

    %% Interface to Shared Dependencies
    GW_WEB --> COMMON
    PP_WEB --> COMMON
    UAM_WEB --> COMMON

    %% Business to Shared Dependencies
    GW_COMP --> COMMON
    PP_COMP --> COMMON
    UAM_COMP --> COMMON

    %% Internal Business Dependencies
    GW_CORE --> GW_DOMAIN
    GW_CORE --> GW_DB
    GW_CORE --> GW_CONFIG
    GW_CONFIG --> GW_DOMAIN
    GW_DB --> GW_DOMAIN

    PP_CORE --> PP_DOMAIN
    PP_CORE --> PP_DB
    PP_CORE --> PP_CONFIG
    PP_CONFIG --> PP_DOMAIN
    PP_DB --> PP_DOMAIN

    UAM_CORE --> UAM_DOMAIN
    UAM_CORE --> UAM_DB
    UAM_CORE --> UAM_CONFIG
    UAM_CONFIG --> UAM_DOMAIN
    UAM_DB --> UAM_DOMAIN

    %% Styling
    classDef interfaceLayer fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef businessLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef sharedLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef forbidden fill:#ffebee,stroke:#c62828,stroke-width:3px,stroke-dasharray: 5 5

    class GW_WEB,PP_WEB,UAM_WEB interfaceLayer
    class GW_CORE,GW_CONFIG,GW_DOMAIN,GW_DB,GW_COMMON,PP_CORE,PP_CONFIG,PP_DOMAIN,PP_DB,PP_COMMON,UAM_CORE,UAM_CONFIG,UAM_DOMAIN,UAM_DB,UAM_COMMON businessLayer
    class COMMON_API,COMMON_CONFIG,COMMON_DOMAIN,COMMON_UTILS sharedLayer
```

### Module Dependencies Rules

#### ✅ **ALLOWED Dependencies**

1. **Interface Layer** → **Business Layer** (Same Module)
   - `ahss-gateway-web` → `ahss-gateway-*` components
   - `ahss-pp-web` → `ahss-pp-*` components  
   - `ahss-uam-web` → `ahss-uam-*` components

2. **Interface Layer** → **Shared Layer**
   - All `*-web` modules → `ahss-common` components

3. **Business Layer** → **Shared Layer**
   - All `ahss-*` components → `ahss-common` components

4. **Within Business Layer** (Same Module Only)
   - `*-core` → `*-domain`, `*-db`, `*-config`
   - `*-config` → `*-domain`
   - `*-db` → `*-domain`

#### ❌ **FORBIDDEN Dependencies (Circular Dependencies)**

1. **Cross-Module Business Dependencies**
   - `ahss-gateway-*` ↔ `ahss-pp-*` ↔ `ahss-uam-*`
   - Business components should NOT depend on other modules' business components

2. **Reverse Dependencies**
   - `ahss-common` → Any other module
   - Business Layer → Interface Layer
   - Shared Layer → Business/Interface Layer

3. **Interface Layer Cross-Dependencies**
   - `ahss-gateway-web` ↔ `ahss-pp-web` ↔ `ahss-uam-web`

### Current Module Dependencies

#### ahss-common (Shared Foundation)
```
ahss-common/
├── common-api-response/     # API response models, status codes
├── common-config/          # Shared configuration (JPA, Security, etc.)
├── common-domain/          # Domain exceptions, validation, context
└── common-utils/           # Cryptography, masking, handlers
```
**Dependencies**: None (foundation layer)

#### ahss-gateway (API Gateway)
```
ahss-gateway/
├── Components/
│   ├── ahss-gateway-common/    # → ahss-common
│   ├── ahss-gateway-config/    # → ahss-gateway-domain
│   ├── ahss-gateway-core/      # → ahss-gateway-domain, ahss-gateway-config
│   ├── ahss-gateway-db/        # → ahss-gateway-domain
│   └── ahss-gateway-domain/    # → ahss-common
└── Interface/
    └── ahss-gateway-web/       # → All ahss-gateway-*, ahss-common
```

#### ahss-product-portal (Product Management)
```
ahss-product-portal/
├── Components/
│   ├── ahss-pp-common/         # → ahss-common
│   ├── ahss-pp-config/         # → ahss-pp-domain, ahss-common
│   ├── ahss-pp-core/           # → ahss-pp-domain, ahss-pp-db, ahss-common
│   ├── ahss-pp-db/             # → ahss-pp-domain, ahss-common
│   └── ahss-pp-domain/         # → ahss-common
└── Interface/
    └── ahss-pp-web/            # → All ahss-pp-*, ahss-common
```

#### ahss-uam (User Access Management)
```
ahss-uam/
├── Components/
│   ├── ahss-uam-common/        # → ahss-common
│   ├── ahss-uam-config/        # → ahss-uam-domain, ahss-common
│   ├── ahss-uam-core/          # → ahss-uam-domain, ahss-uam-db, ahss-common
│   ├── ahss-uam-db/            # → ahss-uam-domain, ahss-common
│   └── ahss-uam-domain/        # → ahss-common
└── Interface/
    └── ahss-uam-web/           # → All ahss-uam-*, ahss-common
```

### Dependency Validation

To ensure no circular dependencies are introduced:

1. **Build Order**: Modules must be built in dependency order:
   ```bash
   mvn clean install -pl ahss-common
   mvn clean install -pl ahss-gateway
   mvn clean install -pl ahss-product-portal  
   mvn clean install -pl ahss-uam
   ```

2. **Dependency Check**: Use Maven dependency plugin to verify:
   ```bash
   mvn dependency:analyze
   mvn dependency:tree
   ```

3. **Code Review Guidelines**:
   - Review all new dependencies in `pom.xml` files
   - Ensure imports follow the dependency hierarchy
   - Reject any cross-module business layer dependencies

### Communication Between Modules

Since direct dependencies between business modules are forbidden, use these patterns:

1. **API Gateway Pattern**: Route requests through `ahss-gateway`
2. **Event-Driven Communication**: Use messaging/events (future enhancement)
3. **Shared Data Models**: Place common DTOs in `ahss-common`
4. **REST Client Pattern**: HTTP calls between services (as seen in UAM → Product Portal)

## Prerequisites

### Java SDK

- **Required**: Java 21 (minimum Java 17 for Spring Boot 3 compatibility)
- Verify installation: `java -version`

### Build Tools

- **Maven**: Install from [Maven Official Site](https://maven.apache.org/index.html)
- Verify installation: `mvn -version`

### Docker

- **Docker Desktop** (for local development)
- Verify installation: `docker --version`

## Recent Fixes & Improvements

✅ **Spring Security Compatibility** (Latest): Fixed version mismatch issues between Spring Boot 3.5.4 and Spring Security components. All modules now use consistent Spring Security 6.5.2.

✅ **Jakarta EE Migration**: Updated servlet dependencies from `javax.servlet-api` to `jakarta.servlet-api` for Spring Boot 3.x compatibility.

✅ **Build Stability**: Resolved `ApplicationContext` loading failures in `ahss-pp-web` module. All tests now pass successfully.

📋 **Detailed Fix Information**: See [SECURITY_FIXES.md](./SECURITY_FIXES.md) for complete details about the Spring Security compatibility fixes.

## Important Build Notes

⚠️ **Dependency Management**: This project uses Spring Boot 3.5.4 with Spring Security 6.5.2. The root `pom.xml` explicitly manages Spring Security versions to ensure compatibility.

⚠️ **Jakarta EE**: Spring Boot 3.x uses Jakarta EE (not Java EE). Ensure all servlet dependencies use `jakarta.servlet-api` instead of `javax.servlet-api`.

⚠️ **Version Consistency**: If you encounter `ClassNotFoundException` for Spring Security classes, verify that all Spring Security components are at version 6.5.2 using the dependency verification commands below.

## Local Setup

### Database

You can run the docker-compose file to bring up your local database.

```shell
docker compose -f ./docker/local-compose.yml up -d
```

### Flyway
Flyway `migrate` is run together with Spring Boot application at startup time. However, you can run your flyway with `maven`
```shell
# ahss-product-portal
mvn flyway:migrate -pl ahss-product-portal/Components/ahss-pp-db

# ahss-uam
mvn flyway:migrate -pl ahss-product-portal/Components/ahss-pp-db
```

### JOOQ
#### Generate sources from Database 
```shell
# ahss-product-portal
mvn generate-sources -P generate-jooq -pl ahss-product-portal/Components/ahss-pp-db -amd

# ahss-uam
mvn generate-sources -P generate-jooq -pl ahss-uam/Components/ahss-uam-db -amd
```

## How to Build and Run

### Quick Start

1. **Start local database**:
   ```shell
   docker compose -f ./docker/postgres-compose.yml up -d
   ```

2. **Build all modules** (recommended for first-time setup):
   ```shell
   # Disable cache for clean first build
   SPRING_PROFILES_ACTIVE=default mvn clean install -Dmaven.build.cache.enabled=false
   ```

3. **Run individual services** (after successful build):
   ```shell
   # Gateway Service (Port 8888)
   mvn spring-boot:run -pl ahss-gateway/Interface/ahss-gateway-web
   
   # Product Portal (Port 8001)
   mvn spring-boot:run -pl ahss-product-portal/Interface/ahss-pp-web
   
   # UAM Service (Port 8011)
   mvn spring-boot:run -pl ahss-uam/Interface/ahss-uam-web
   ```

### Build Profiles

#### Profile `default` (Development)
- **Use for**: Local development and testing
- **Command options**:
    ```shell
    # Option 1: Using system property (with cache disabled - recommended)
    mvn clean install -Dspring.profiles.active=default -Dmaven.build.cache.enabled=false
    
    # Option 2: Using environment variable (with cache disabled - recommended)
    SPRING_PROFILES_ACTIVE=default mvn clean install -Dmaven.build.cache.enabled=false
    
    # Option 3: With cache enabled (only after successful initial builds, no clean)
    SPRING_PROFILES_ACTIVE=default mvn install -Dmaven.build.cache.enabled=true
    ```

#### Profile `integration` (Pre-push)
- **Use for**: Comprehensive testing before pushing code
- **Note**: This profile runs expensive integration tests
    ```shell
    # Recommended: Disable cache for clean integration testing
    SPRING_PROFILES_ACTIVE=integration mvn clean install -Dmaven.build.cache.enabled=false
    
    # Alternative: With cache (only if previous builds were successful, no clean)
    SPRING_PROFILES_ACTIVE=integration mvn install -Dmaven.build.cache.enabled=true
    ```

### Troubleshooting Build Issues

#### Resume from Failed Module
If a build fails on a specific module, you can resume from that point:
```shell
# Resume from ahss-pp-web module
mvn clean install -rf :ahss-pp-web -Dspring.profiles.active=default
```

#### Common Build Problems

1. **ApplicationContext Loading Failures**:
   - Ensure Spring Security versions are consistent (6.5.2)
   - Check that Jakarta EE dependencies are used (not Java EE)
   - Verify Java 21 is being used

2. **Dependency Conflicts**:
   ```shell
   # Check dependency tree for conflicts
   mvn dependency:tree -pl ahss-product-portal/Interface/ahss-pp-web
   ```

3. **Test Failures**:
   ```shell
   # Skip tests if needed (not recommended for production)
   mvn clean install -DskipTests
   
   # Run tests with verbose output
   mvn test -pl ahss-product-portal/Interface/ahss-pp-web -Dspring.profiles.active=default
   ```

4. **Cache-Related Issues**:
   ```shell
   # Disable Maven cache for clean builds (recommended for troubleshooting)
   mvn clean install -Dmaven.build.cache.enabled=false -Dspring.profiles.active=default
   
   # Clear local Maven repository cache
   mvn dependency:purge-local-repository
   ```

### Build Individual Modules

#### Single Module Build
```shell
# Build specific module with dependencies
mvn clean install -pl ahss-product-portal -amd -Dspring.profiles.active=default

# Build multiple modules
mvn clean install -pl ahss-gateway,ahss-product-portal -amd -Dspring.profiles.active=default
```

#### Module-Specific Commands
```shell
# Product Portal only
mvn clean install -pl ahss-product-portal/Interface/ahss-pp-web -Dspring.profiles.active=default

# Gateway only  
mvn clean install -pl ahss-gateway/Interface/ahss-gateway-web -Dspring.profiles.active=default

# UAM only
mvn clean install -pl ahss-uam/Interface/ahss-uam-web -Dspring.profiles.active=default
```

### Advanced Build Options

#### Build with Cache (Faster Builds)
⚠️ **Note**: Only use cache after successful initial builds. Disable cache for troubleshooting.
```shell
# Enable cache for faster subsequent builds (after initial setup)
mvn clean install -Dspring.profiles.active=default -Dmaven.build.cache.enabled=true
```

#### Build without Cache (Recommended for Troubleshooting)
```shell
# Disable cache for clean builds and issue resolution
mvn clean install -Dspring.profiles.active=default -Dmaven.build.cache.enabled=false
```

#### Generate Test Reports
```shell
# Generate surefire reports for all modules
mvn clean install surefire-report:report -Daggregate=true -Dspring.profiles.active=default
```

### Dependency Verification

#### Check Spring Security Versions
Verify that all Spring Security components are at version 6.5.2:
```shell
# Check Spring Security dependencies
mvn dependency:tree -pl ahss-product-portal/Interface/ahss-pp-web -Dincludes="org.springframework.security:*"
```

#### Verify Jakarta EE Dependencies
Ensure Jakarta EE (not Java EE) dependencies are used:
```shell
# Should show jakarta.servlet-api, not javax.servlet-api
mvn dependency:tree -pl ahss-product-portal/Interface/ahss-pp-web -Dincludes="*servlet*"
```

## Build Docker Image with `Google Jib Plugin`

### Local Docker

| Docker Service      | Container Name      | Version/Tag    | Port |
|---------------------|---------------------|----------------|------|
| Postgres            | ahss-local-postgres | 15.7           | 5432 |
| AHSS Gateway        | ahss-gateway-web    | 0.0.1-SNAPSHOT | 8888 |
| AHSS Product Portal | ahss-pp-web         | 0.0.1-SNAPSHOT | 8001 |
| AHSS UAM            | ahss-uam-web        | 0.0.1-SNAPSHOT | 8011 |

#### Profiles

We can set spring profile with:

- JVM Arg: `-D spring.profiles.active=default`
- System Env: `SPRING_PROFILES_ACTIVE=default`

| Spring Profile | Properties/Yaml               | Remarks                                                                                                                                       | |
|----------------|-------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------|-|
| default        | `application.yml`             |                                                                                                                                               | |
| dev            | `application-dev.yml`         |                                                                                                                                               | |
| sit            | `application-sit.yml`         |                                                                                                                                               | |
| uat            | `application-uat.yml`         |                                                                                                                                               | |
| prod           | `application-prod.yml`        |                                                                                                                                               | |
| integration    | `application-integration.yml` | This profile `MUST` be used before pushing any code to repo. `pre-push` script should be copied from `./script/` directory to `./.git/hooks/` | |

#### **ahss-gateway-web**

- Build module and create docker image

```shell
# build modules and dependencies
mvn clean install -pl ahss-gateway -amd -P local-docker -D spring.profiles.active=integration
# build image       
mvn -pl ahss-gateway/Interface/ahss-gateway-web compile jib:dockerBuild -P local-docker
```

- Run docker

```shell
# run docker container
docker run --name ahss-gateway-web -p 8888:8888 ahss-gateway-web:0.0.1-SNAPSHOT
```

#### **ahss-pp-web**

- Build module and create docker image

```shell
# build modules and dependencies
mvn clean install -pl ahss-product-portal -amd -P local-docker -D spring.profiles.active=integration           
# build image
mvn -pl ahss-product-portal/Interface/ahss-pp-web compile jib:dockerBuild -P local-docker
```

- Run docker

```shell
# run docker container
docker run -e "DB_URL=****************************************************" \
-e "DB_USERNAME=ahss" \
-e "DB_PASSWORD=password" \
-p 8001:8001 --name ahss-pp-web ahss-pp-web:0.0.1-SNAPSHOT
```

#### **ahss-uam-web**

- Build module and create docker image

```shell
# build modules and dependencies
mvn clean install -pl ahss-uam -amd -P local-docker -D spring.profiles.active=integration           
# build image
mvn -pl ahss-uam/Interface/ahss-uam-web compile jib:dockerBuild -P local-docker
```

- Run docker

```shell
# run docker container
docker run -e "DB_URL=****************************************************" \
-e "DB_USERNAME=ahss" \
-e "DB_PASSWORD=password" \
-p 8011:8011 --name ahss-uam-web ahss-uam-web:0.0.1-SNAPSHOT
```

#### All-In-One docker compose

- Bring up the services

```shell
docker compose -f ./docker/all-in-one.yml up
```

- Bring down the services

```shell
docker compose -f ./docker/all-in-one.yml down
```

### Azure Container Registry

Build and push docker image to ACR with profile `azure-acr`

```shell
mvn -pl ahss-gateway/Interface/ahss-gateway-web compile jib:dockerBuild -P azure-acr
```

### All-In-One `run-all.sh` script

- Usage

```shell
./scripts/run-all.sh -h
# Usage: ./scripts/run-all.sh [-p|--profile <spring-profile>] [-m|--maven-profile <maven-profile>] [-d|--docker-compose <compose-file>]
```

- Example

```shell
./scripts/run-all.sh -p integration -m local-docker
```

## Git Hooks

### Pre-push

- Copy the `pre-push` script to your project `.git/hooks/` folder

```shell
cp ./scripts/pre-push ./.git/hooks/
```
