package com.ahss.pp.db.repository;

import com.ahss.pp.db.model.UserProduct;
import io.micrometer.observation.annotation.Observed;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
@Observed
public interface UserProductRepository extends JpaRepository<UserProduct, Integer> {

    List<UserProduct> findByUidAndIsFavorite(String uid, Boolean isFavorite);

    Optional<UserProduct> findFirstByProduct_ProductIdAndUid(Integer productId, String uid);


    @Query("SELECT DISTINCT new UserProduct(p, up.isFavorite) FROM Product p " +
        "JOIN p.modules m " +
        "JOIN m.packageModules pm " +
        "JOIN pm.aPackage pk " +
        "JOIN pk.plan pl " +
        "JOIN TenantPlan tp ON pl.planId = tp.planId " +
        "LEFT JOIN UserProduct up ON up.product = p AND up.uid = :userId " +
        "WHERE tp.tenantCode IN :tenantCodes AND p.productCode IN :productCodes")
    List<UserProduct> findProductsByTenantCodesWithFavorite(@Param("tenantCodes") List<String> tenantCodes,
                                                            @Param("productCodes") List<String> productCodes,
                                                            @Param("userId") String userId);

}
