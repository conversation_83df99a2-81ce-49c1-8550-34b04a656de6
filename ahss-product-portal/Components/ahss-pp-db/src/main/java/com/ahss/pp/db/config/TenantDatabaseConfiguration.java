package com.ahss.pp.db.config;

import javax.sql.DataSource;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;

@Configuration
public class TenantDatabaseConfiguration {

  @Primary
  @Profile("local-db")
  @Bean(name = "datasource")
  @ConfigurationProperties(prefix = "spring.datasource.local")
  public DataSource localConnectionDataSource() {
    return DataSourceBuilder.create().build();
  }

  @Primary
  @Profile("!local-db")
  @Bean(name = "datasource")
  @ConfigurationProperties(prefix = "spring.datasource")
  public DataSource connectionDataSource() {
    return DataSourceBuilder.create().build();
  }
}