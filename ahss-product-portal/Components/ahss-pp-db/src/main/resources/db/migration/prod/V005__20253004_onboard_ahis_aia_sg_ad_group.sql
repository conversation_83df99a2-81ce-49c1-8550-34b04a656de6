DO
$$
    DECLARE
        v_tenant_code        TEXT   := 'AIA_SG';
        v_module_id          BIGINT := 1;

        -- ========= Plan values =========
        v_plan_name          TEXT   := 'Singapore billing plan';
        v_plan_discount_rate INT    := 0;
        v_plan_type          TEXT   := 'SUBSCRIPTION';
        v_plan_status        TEXT   := 'ACTIVE';
        v_plan_start_date    TEXT   := '2024-01-01';
        v_plan_end_date      TEXT   := '9999-12-31';

        -- ========= Package values =========
        v_package_name       TEXT   := 'Insight Package SG';
        v_package_type       TEXT   := 'Type1';
        v_package_price      INT    := 1000;
        v_package_version    INT    := 1;
        v_package_status     TEXT   := 'ACTIVE';
        v_package_start_date TEXT   := '2024-01-01';
        v_package_end_date   TEXT   := '9999-12-31';

        -- ========= Temp variables =========
        new_plan_id          BIGINT;
        existing_plan_id     BIGINT;
        new_package_id       BIGINT;
        existing_package_id  BIGINT;
    BEGIN
        -- Drop tenant_id column of tenant_plan table
        IF EXISTS (SELECT 1
                   FROM information_schema.columns
                   WHERE table_schema = 'ahpp'
                     AND table_name = 'tenant_plan'
                     AND column_name = 'tenant_id') THEN
            ALTER TABLE AHPP.tenant_plan
                DROP COLUMN tenant_id;

            BEGIN
                ALTER TABLE ahpp.tenant_plan
                    DROP CONSTRAINT IF EXISTS tenant_plan_pkey;
            EXCEPTION
                WHEN others THEN
            END;

            ALTER TABLE ahpp.tenant_plan
                ADD PRIMARY KEY (tenant_code, plan_id);
        END IF;

        -- Ensure sequence for package_id is correct
        IF (SELECT MAX(plan_id) FROM AHPP.plan) IS NOT NULL THEN
            PERFORM setval('AHPP.plan_plan_id_seq',
                           GREATEST(
                               (SELECT MAX(plan_id) FROM AHPP.plan),
                               (SELECT last_value FROM AHPP.plan_plan_id_seq)
                           ));
        END IF;

        IF (SELECT MAX(package_id) FROM AHPP.package) IS NOT NULL THEN
            PERFORM setval('AHPP.package_package_id_seq',
                           GREATEST(
                               (SELECT MAX(package_id) FROM AHPP.package),
                               (SELECT last_value FROM AHPP.package_package_id_seq)
                           ));
        END IF;

        IF (SELECT MAX(package_module_id) FROM AHPP.package_module) IS NOT NULL THEN
            PERFORM setval('AHPP.package_module_package_module_id_seq',
                           GREATEST(
                               (SELECT MAX(package_module_id) FROM AHPP.package_module),
                               (SELECT last_value FROM AHPP.package_module_package_module_id_seq)
                           ));
        END IF;

        --Upsert plan ---
        SELECT plan_id
        INTO existing_plan_id
        FROM AHPP.plan
        WHERE name = v_plan_name
          AND plan_type = v_plan_type::plan_type;

        IF existing_plan_id IS NULL THEN
            INSERT INTO AHPP.plan (name, discount_rate, start_date, end_date, plan_type, plan_status, created_at, updated_at, created_by, updated_by)
            VALUES (v_plan_name,
                    v_plan_discount_rate,
                    v_plan_start_date::DATE,
                    v_plan_end_date::DATE,
                    v_plan_type::plan_type,
                    v_plan_status::plan_status,
                    CURRENT_TIMESTAMP,
                    CURRENT_TIMESTAMP,
                    'system',
                    'system')
            RETURNING plan_id INTO new_plan_id;
        ELSE
            UPDATE AHPP.plan
            SET discount_rate = v_plan_discount_rate,
                start_date    = v_plan_start_date::DATE,
                end_date      = v_plan_end_date::DATE,
                plan_status   = v_plan_status::plan_status,
                updated_at    = CURRENT_TIMESTAMP,
                updated_by    = 'system'
            WHERE plan_id = existing_plan_id;
            new_plan_id := existing_plan_id;
        END IF;

        -- Insert into tenant_plan (only if not exist)
        IF NOT EXISTS (SELECT 1
                       FROM AHPP.tenant_plan
                       WHERE tenant_code = v_tenant_code
                         AND plan_id = new_plan_id) THEN
            INSERT INTO AHPP.tenant_plan (tenant_code, plan_id, assigned_at, created_at, updated_at, created_by, updated_by)
            VALUES (v_tenant_code,
                    new_plan_id,
                    v_plan_start_date::DATE,
                    CURRENT_TIMESTAMP,
                    CURRENT_TIMESTAMP,
                    'system',
                    'system');
        END IF;

        -- Upsert package
        SELECT package_id
        INTO existing_package_id
        FROM AHPP.package
        WHERE plan_id = new_plan_id
          AND name = v_package_name;

        IF existing_package_id IS NULL THEN
            INSERT INTO AHPP.package (plan_id, name, type, price, package_status, start_date, end_date, version, created_at, updated_at, created_by,
                                      updated_by)
            VALUES (new_plan_id,
                    v_package_name,
                    v_package_type,
                    v_package_price,
                    v_package_status::package_status,
                    v_package_start_date::DATE,
                    v_package_end_date::DATE,
                    v_package_version,
                    CURRENT_TIMESTAMP,
                    CURRENT_TIMESTAMP,
                    'system',
                    'system')
            RETURNING package_id INTO new_package_id;
        ELSE
            UPDATE AHPP.package
            SET type           = v_package_type,
                price          = v_package_price,
                package_status = v_package_status::package_status,
                start_date     = v_package_start_date::DATE,
                end_date       = v_package_end_date::DATE,
                version        = v_package_version,
                updated_at     = CURRENT_TIMESTAMP,
                updated_by     = 'system'
            WHERE package_id = existing_package_id;
            new_package_id := existing_package_id;
        END IF;

        -- Insert into package_module (if not exists)
        IF NOT EXISTS (SELECT 1
                       FROM AHPP.package_module
                       WHERE package_id = new_package_id
                         AND module_id = v_module_id) THEN
            INSERT INTO AHPP.package_module (package_id, module_id, created_at, updated_at, created_by, updated_by)
            VALUES (new_package_id,
                    v_module_id,
                    CURRENT_TIMESTAMP,
                    CURRENT_TIMESTAMP,
                    'system',
                    'system');
        END IF;
    END
$$;

