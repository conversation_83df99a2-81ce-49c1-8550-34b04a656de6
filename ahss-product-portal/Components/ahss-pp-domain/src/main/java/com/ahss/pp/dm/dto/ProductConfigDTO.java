package com.ahss.pp.dm.dto;

import com.ahss.pp.db.enums.ProductConfigStatus;
import com.ahss.pp.db.enums.ProductConfigType;
import com.ahss.pp.db.enums.ValueFormatType;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class ProductConfigDTO {

    private Integer productId;
    private String productCode;
    @NotNull
    private ProductConfigType configType;
    @NotNull
    private String configValue;
    @NotNull
    private ValueFormatType configValueFormatType;
    @NotNull
    private String regex;
    @NotNull
    private ProductConfigStatus productConfigStatus;
}
