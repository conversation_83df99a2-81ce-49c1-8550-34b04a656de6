package com.ahss.pp.dm.mapper;

import com.ahss.pp.db.model.Product;
import com.ahss.pp.db.model.ProductConfig;
import com.ahss.pp.dm.dto.ProductConfigDTO;
import com.ahss.pp.dm.dto.ProductDTO;
import java.util.List;
import org.mapstruct.AfterMapping;
import org.mapstruct.BeanMapping;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    componentModel = MappingConstants.ComponentModel.SPRING,
    builder = @Builder(disableBuilder = true)
)
public interface ProductMapper {

    @Mapping(target = "productConfigs", source = "productConfigs")
    Product toEntity(ProductDTO productDTO);

    @Mapping(target = "productConfigs", source = "productConfigs")
    ProductDTO toDto(Product product);

    List<ProductConfig> toProductConfigList(List<ProductConfigDTO> productConfigDTOS);

    @AfterMapping
    default void linkProductToConfigs(@MappingTarget Product product) {
        if (product.getProductConfigs() != null) {
            for (ProductConfig config : product.getProductConfigs()) {
                config.setProductCode(product.getProductCode());
                config.setProduct(product);
            }
        }
    }

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    Product partialUpdate(ProductDTO productDTO, @MappingTarget Product product);

}
