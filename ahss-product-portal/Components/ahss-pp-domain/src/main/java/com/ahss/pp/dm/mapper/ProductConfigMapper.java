package com.ahss.pp.dm.mapper;

import com.ahss.pp.db.model.ProductConfig;
import com.ahss.pp.dm.dto.ProductConfigDTO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

@Mapper(
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    componentModel = MappingConstants.ComponentModel.SPRING
)
public interface ProductConfigMapper {

    ProductConfig toEntity(ProductConfigDTO productConfigDTO);

    ProductConfigDTO toDto(ProductConfig productConfig);

    List<ProductConfig> toEntityList(List<ProductConfigDTO> productConfigDTOS);

}
