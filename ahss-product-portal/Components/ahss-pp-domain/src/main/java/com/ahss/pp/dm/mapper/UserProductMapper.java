package com.ahss.pp.dm.mapper;

import com.ahss.pp.db.model.UserProduct;
import com.ahss.pp.dm.dto.UserProductDTO;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

@Mapper(
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    componentModel = MappingConstants.ComponentModel.SPRING,
    builder = @Builder(disableBuilder = true)
)
public interface UserProductMapper {

    @Mapping(source = "product.productId", target = "productId")
    @Mapping(source = "product.productCode", target = "productCode")
    @Mapping(source = "product.productName", target = "productName")
    @Mapping(source = "product.description", target = "description")
    @Mapping(source = "product.productStatus", target = "productStatus")
    @Mapping(source = "product.productConfigs", target = "productConfigs")
    UserProductDTO toDto(UserProduct userProductDTO);

}
