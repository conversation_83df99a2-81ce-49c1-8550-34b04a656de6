package com.ahss.pp.dm.dto;

import com.ahss.common.domain.validation.ValidEnum;
import com.ahss.pp.db.enums.ProductStatus;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Data
public class ProductDTO {

    private Integer productId;
    @Size(max = 255, message = "Product Code must be at most 255 characters")
    private String productCode;
    @Size(max = 255, message = "Product Name must be at most 255 characters")
    private String productName;
    private String description;
    @ValidEnum(enumClass = ProductStatus.class, message = "Invalid product status")
    private ProductStatus productStatus;
    private List<ProductConfigDTO> productConfigs;
}
