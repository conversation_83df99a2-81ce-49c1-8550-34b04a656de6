package com.ahss.pp.dm.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.ahss.pp.db.model.ProductConfig;
import com.ahss.pp.dm.dto.ProductConfigDTO;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

class ProductConfigMapperTest {

    private ProductConfigMapper productConfigMapper;

    @BeforeEach
    void setUp() {
        productConfigMapper = Mappers.getMapper(ProductConfigMapper.class);
    }

    @Test
    void testToEntity() {
        ProductConfigDTO dto = new ProductConfigDTO();
        dto.setConfigValue("value1");

        ProductConfig entity = productConfigMapper.toEntity(dto);

        assertNotNull(entity);
        assertEquals("value1", entity.getConfigValue());
    }

    @Test
    void testToEntityNull() {
        ProductConfigDTO dto = null;
        ProductConfig entity = productConfigMapper.toEntity(dto);
        assertNull(entity);
    }

    @Test
    void testToDto() {
        ProductConfig entity = new ProductConfig();
        entity.setConfigValue("value1");

        ProductConfigDTO dto = productConfigMapper.toDto(entity);

        assertNotNull(dto);
        assertEquals("value1", dto.getConfigValue());
    }

    @Test
    void testToDtoNull() {
        ProductConfig entity = null;
        ProductConfigDTO dto = productConfigMapper.toDto(entity);
        assertNull(dto);
    }

    @Test
    void testToEntityList() {
        ProductConfigDTO dto1 = new ProductConfigDTO();
        dto1.setConfigValue("value1");

        ProductConfigDTO dto2 = new ProductConfigDTO();
        dto2.setConfigValue("value2");

        List<ProductConfigDTO> dtoList = new ArrayList<>();
        dtoList.add(dto1);
        dtoList.add(dto2);

        List<ProductConfig> entityList = productConfigMapper.toEntityList(dtoList);

        assertNotNull(entityList);
        assertEquals(2, entityList.size());
    }

}
