package com.ahss.pp.dm.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.ahss.pp.db.enums.ProductStatus;
import com.ahss.pp.db.model.Product;
import com.ahss.pp.db.model.UserProduct;
import com.ahss.pp.dm.dto.UserProductDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

class UserProductMapperTest {

    private UserProductMapper userProductMapper;

    @BeforeEach
    void setUp() {
        // Initialize the mapper
        userProductMapper = Mappers.getMapper(UserProductMapper.class);
    }

    @Test
    void shouldMapUserProductToDTO() {
        // Arrange
        Product product = new Product();
        product.setProductId(1);
        product.setProductCode("P12345");
        product.setProductName("Test Product");
        product.setDescription("Test description");
        product.setProductStatus(ProductStatus.ACTIVE);
        product.setProductConfigs(null);  // Assuming no configs for simplicity

        UserProduct userProduct = new UserProduct();
        userProduct.setProduct(product);

        // Act
        UserProductDTO userProductDTO = userProductMapper.toDto(userProduct);

        // Assert
        assertNotNull(userProductDTO);
        assertEquals(product.getProductId(), userProductDTO.getProductId());
        assertEquals(product.getProductCode(), userProductDTO.getProductCode());
        assertEquals(product.getProductName(), userProductDTO.getProductName());
        assertEquals(product.getDescription(), userProductDTO.getDescription());
        assertEquals(product.getProductStatus(), userProductDTO.getProductStatus());
        assertNull(userProductDTO.getProductConfigs());
    }
}
