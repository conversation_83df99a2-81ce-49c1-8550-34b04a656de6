package com.ahss.pp.dm.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.ahss.pp.db.enums.ProductStatus;
import com.ahss.pp.db.model.Product;
import com.ahss.pp.db.model.ProductConfig;
import com.ahss.pp.dm.dto.ProductConfigDTO;
import com.ahss.pp.dm.dto.ProductDTO;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {ProductMapperImpl.class})
class ProductMapperTest {

    @Autowired
    private ProductMapper productMapper;

    @Test
    void toEntity() {
        ProductDTO dto = ProductDTO.builder().productId(1).productName("Insights Studio").build();
        var result = productMapper.toEntity(dto);
        assertNotNull(result);
        assertEquals(dto.getProductId(), result.getProductId());
        assertEquals(dto.getProductName(), result.getProductName());
    }

    @Test
    void testToEntity() {
        ProductDTO productDTO = new ProductDTO();
        productDTO.setProductCode("P001");
        productDTO.setProductName("Product 1");

        ProductConfigDTO configDTO = new ProductConfigDTO();
        configDTO.setConfigValue("value1");

        List<ProductConfigDTO> configDTOs = new ArrayList<>();
        configDTOs.add(configDTO);
        productDTO.setProductConfigs(configDTOs);

        Product product = productMapper.toEntity(productDTO);

        assertNotNull(product);
        assertEquals("P001", product.getProductCode());
        assertEquals("Product 1", product.getProductName());
        assertNotNull(product.getProductConfigs());
        assertEquals(1, product.getProductConfigs().size());
    }

    @Test
    void testToEntityNull() {
        ProductDTO productDTO = null;
        Product product = productMapper.toEntity(productDTO);
        assertNull(product);
    }

    @Test
    void testToDto() {
        Product product = new Product();
        product.setProductCode("P001");
        product.setProductName("Product 1");

        ProductConfig config = new ProductConfig();
        config.setConfigValue("value1");
        List<ProductConfig> configs = new ArrayList<>();
        configs.add(config);
        product.setProductConfigs(configs);

        ProductDTO productDTO = productMapper.toDto(product);

        assertNotNull(productDTO);
        assertEquals("P001", productDTO.getProductCode());
        assertEquals("Product 1", productDTO.getProductName());
        assertNotNull(productDTO.getProductConfigs());
        assertEquals(1, productDTO.getProductConfigs().size());
    }

    @Test
    void testToDtoNullValue() {
        Product product = null;
        ProductDTO productDTO = productMapper.toDto(product);
        assertNull(productDTO);
    }

    @Test
    void testToProductSysConfigListNull() {
        Product product = new Product();
        ProductDTO productDTO = productMapper.toDto(product);
        assertNull(productDTO.getProductConfigs());
    }

    @Test
    void testPartialUpdate() {
        ProductDTO productDTO = new ProductDTO();
        productDTO.setProductCode("P002");
        productDTO.setProductName("Updated Product");
        productDTO.setDescription("Updated Description");

        ProductConfigDTO configDTO1 = new ProductConfigDTO();
        configDTO1.setConfigValue("value1");

        ProductConfigDTO configDTO2 = new ProductConfigDTO();
        configDTO2.setConfigValue("value2");

        ArrayList<ProductConfigDTO> configDTOs = new ArrayList<>();
        configDTOs.add(configDTO1);
        configDTOs.add(configDTO2);

        productDTO.setProductConfigs(configDTOs);

        Product product = new Product();
        product.setProductName("Old Product");
        product.setProductCode("P001");

        productMapper.partialUpdate(productDTO, product);

        assertNotNull(product);
        assertEquals("P002", product.getProductCode());
        assertEquals("Updated Description", product.getDescription());
        assertEquals(2, product.getProductConfigs().size());
    }

    @Test
    void testPartialUpdateNull() {
        ProductDTO productDTO = null;

        Product product = new Product();
        product.setProductName("Old Product");
        product.setProductCode("P001");

        productMapper.partialUpdate(productDTO, product);

        assertNotNull(product);
        assertEquals("Old Product", product.getProductName());
        assertEquals("P001", product.getProductCode());  // Unchanged
    }

    @Test
    void testPartialUpdateOnlyStatus() {
        ProductDTO productDTO = new ProductDTO();
        productDTO.setProductStatus(ProductStatus.ACTIVE);

        Product product = new Product();
        product.setProductName("Old Product");
        product.setProductCode("P001");
        product.setProductStatus(ProductStatus.DRAFT);

        productMapper.partialUpdate(productDTO, product);

        assertNotNull(product);
        assertEquals("Old Product", product.getProductName());
        assertEquals("P001", product.getProductCode());
        assertEquals(ProductStatus.ACTIVE, product.getProductStatus());
    }

    @Test
    void testLinkProductToConfigs() {
        Product product = new Product();
        product.setProductCode("P001");

        ProductConfig config1 = new ProductConfig();
        ProductConfig config2 = new ProductConfig();
        List<ProductConfig> configs = new ArrayList<>();
        configs.add(config1);
        configs.add(config2);
        product.setProductConfigs(configs);

        productMapper.linkProductToConfigs(product);

        for (ProductConfig config : product.getProductConfigs()) {
            assertEquals("P001", config.getProductCode());
            assertEquals(product, config.getProduct());
        }
    }
}
