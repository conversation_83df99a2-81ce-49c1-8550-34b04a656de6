# AH Product Portal
## Server
- Application Name: `ahss-product-portal`
- Port: `8001`

## SpringDoc - Open API
We are using `springdoc` instead of `springfox` because of its support for Spring Boot 3.x.x 
- http://localhost:8001/swagger-ui/index.html
- http://localhost:8001/v3/api-docs
![springdoc-openapi-sample.png](./ahpp-docs/resources/springdoc-openapi-sample.png)
### Sample API Documentation
```java
@Operation(summary = "Get list of products", description = "Get all products' info")
@io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200",
    description = "Successfully retrieved item",
    content = @Content(mediaType = "application/json",
        schema = @Schema(implementation = ApiResponse.class),
        examples = @ExampleObject(name = "Successful retrieve product list", value = """
            {
              "status": "SUCCESS",
              "responseCode": {
                "code": RC_200_000,
                "message": "Success",
                "detailMessage": "Request has been processed successfully"
              },
              "data": {
                "id": 1,
                "productName": "Insights Studio"
              },
              "request": null,
              "actions": null
            }
            """)))
@GetMapping(value = "/products")
public ResponseEntity<ApiResponse<ProductDTO, Object>> getProduct() {
    return ResponseEntity.ok(ApiResponse.<ProductDTO, Object>builder().status(ResponseStatus.SUCCESS)
        .responseCode(AHSSResponseCode.RC_200_000).data(new ProductDTO(1, "Insights Studio")).build());
}
```  
### Secure your API doc endpoints
- [SecurityConfig.java](./Components/ahss-pp-config/src/main/java/com/ahss/pp/web/config/SecurityConfig.java)