{"connectionString": "${APPLICATIONINSIGHTS_CONNECTION_STRING:-}", "role": {"name": "ahss-pp-web"}, "sampling": {"overrides": [{"telemetryType": "request", "attributes": [{"key": "url.path", "value": "/actuator/health", "matchType": "strict"}], "percentage": 10}, {"telemetryType": "request", "attributes": [{"key": "url.path", "value": "/actuator/*", "matchType": "regexp"}], "percentage": 20}, {"telemetryType": "dependency", "attributes": [{"key": "db.system", "value": ".*", "matchType": "regexp"}], "percentage": 50}]}, "customDimensions": {"SpringProfile": "${SPRING_PROFILES_ACTIVE:-dev}"}}