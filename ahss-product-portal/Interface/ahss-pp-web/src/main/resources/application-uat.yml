spring:
  cloud:
    azure:
      credential:
        managed-identity-enabled: true
        client-id: ${DB_CLIENT_ID:638c20cd-0046-43c6-a521-ff475cb0859c}
  datasource:
    url: ${DB_URL:*****************************************}
    driver-class-name: org.postgresql.Driver
    username: ${DB_USERNAME:ahss}
    password: ${DB_PASSWORD:password}
    azure:
      passwordless-enabled: ${PASSWORDLESS_ENABLED:false}
    hikari:
      maximum-pool-size: 10
  flyway:
    schemas:
      - ahpp
    connect-retries: 3
    locations:
      - classpath:db/migration/uat

security:
  jwt:
    issuers:
      - jwk-set-uri: ${JWK_SET_URI_WORKFORCE:https://aiatest.okta.com/oauth2/aus1zilsv95ktmdzW0h8/v1/keys}
        issuer: ${ISSUER_URI_WORKFORCE:https://aiatest.okta.com/oauth2/aus1zilsv95ktmdzW0h8}
        audience: ${JWT_AUDIENCE_WORKFORCE:Amplify Health Product Portal}
      - jwk-set-uri: ${JWK_SET_URI_CIAM:https://ah-uat.okta.com/oauth2/aus5ya2z0vzlB2Irf3l7/v1/keys}
        issuer: ${ISSUER_URI_CIAM:https://ah-uat.okta.com/oauth2/aus5ya2z0vzlB2Irf3l7}
        audience: ${JWT_AUDIENCE_CIAM:ah_server}
