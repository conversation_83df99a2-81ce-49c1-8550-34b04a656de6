server:
  port: 8001

spring:
  application:
    name: "ahss-product-portal"
  cloud:
    azure:
      credential:
        managed-identity-enabled: true
        client-id: ${DB_CLIENT_ID:638c20cd-0046-43c6-a521-ff475cb0859c}
  datasource:
    url: ${DB_URL:*****************************************}
    driver-class-name: org.postgresql.Driver
    username: ${DB_USERNAME:ahss}
    password: ${DB_PASSWORD:password}
    azure:
      passwordless-enabled: ${PASSWORDLESS_ENABLED:false}
    hikari:
      maximum-pool-size: 10
  flyway:
    schemas:
      - ahpp
    connect-retries: 3
    locations:
      - classpath:db/migration/dev
    fail-on-missing-locations: false
    create-schemas: true
  jpa:
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        use_sql_comments: true
  thread-executor:
    task: virtual
    servlet: virtual

management:
  tracing:
    sampling:
      probability: 0.0
  zipkin:
    tracing:
      endpoint: ${METRICS_COLLECTOR_ENDPOINT:}
  endpoints:
    web:
      exposure:
        include: prometheus,health,metrics
    #        include: "*"
    enabled-by-default: true
  endpoint:
    health:
      show-details: always
  metrics:
    distribution:
      percentiles-histogram:
        http:
          server:
            requests: false
    enable:
      all: false

ahpp:
  swagger:
    show-hidden: ${SWAGGER_SHOW_HIDDEN:true}
  service-prefix: ${SERVICE_PREFIX:/ahss-pp}
  adgroup-pattern: ${AD_GROUP_PATTERN:(?<prefix>\w+)_(?<product>\w+)_(?<tenant>\w+)_(?<market>\w+)_(?<authority>\w+)_(?<suffix>\w+)}

springdoc:
  packages-to-scan:
    - com.ahss.pp.web.controller
  writer-with-default-pretty-printer: true
  enable-spring-security: true
  api-docs:
    path: /v3/api-docs
  webjars:
    prefix: /swagger-doc/webjars
  swagger-ui:
    url: ${ahpp.service-prefix}/v3/api-docs
    config-url: ${ahpp.service-prefix}/v3/api-docs/swagger-config
    disable-swagger-default-url: true

security:
  jwt:
    issuers:
      - jwk-set-uri: ${JWK_SET_URI_WORKFORCE:https://aiasec.oktapreview.com/oauth2/ausgr4dyrr9dsFkpM1d7/v1/keys}
        issuer: ${ISSUER_URI_WORKFORCE:https://aiasec.oktapreview.com/oauth2/ausgr4dyrr9dsFkpM1d7}
        audience: ${JWT_AUDIENCE_WORKFORCE:Amplify Health Product Portal DEV}
      - jwk-set-uri: ${JWK_SET_URI_CIAM:https://ah-sit.okta.com/oauth2/aus5lw9no9x4k0QoO3l7/v1/keys}
        issuer: ${ISSUER_URI_CIAM:https://ah-sit.okta.com/oauth2/aus5lw9no9x4k0QoO3l7}
        audience: ${JWT_AUDIENCE_CIAM:ah_server}

logging:
  level:
    com.ahss.pp.config: DEBUG
    org.springframework.security: DEBUG
    org.flywaydb: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.jdbc.BasicBinder: TRACE
    org.springframework.web: INFO
    org.springframework.http: INFO
    com:
      microsoft:
        applicationinsights: DEBUG


