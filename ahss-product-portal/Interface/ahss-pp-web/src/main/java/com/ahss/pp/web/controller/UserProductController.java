package com.ahss.pp.web.controller;

import static com.ahss.common.api.AHSSResponseCode.RC_200_000;

import com.ahss.common.api.ApiResponse;
import com.ahss.common.api.ResponseStatus;
import com.ahss.pp.core.service.UserProductService;
import com.ahss.pp.db.model.UserProduct;
import com.ahss.pp.dm.dto.ProductFavoriteRequest;
import com.ahss.pp.dm.dto.UserProductDTO;
import com.ahss.pp.dm.mapper.UserProductMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping(value = "/v1/api/pp/user-product")
@Validated
@Tag(name = "User Product", description = "User Product Management")
public class UserProductController {

    private final UserProductService userProductService;
    private final UserProductMapper userProductMapper;

    @Operation(summary = "Get list of user's products", description = "Get all user's products info")
    @GetMapping()
    public ResponseEntity<ApiResponse<List<UserProductDTO>, Object>> getProduct() {
        log.info("Processing get user's product ");
        List<UserProduct> productList = userProductService.getProducts();
        var result = productList.stream()
                                .map(userProductMapper::toDto)
                                .toList();
        return ResponseEntity.ok(ApiResponse.<List<UserProductDTO>, Object>builder()
                                            .status(ResponseStatus.SUCCESS)
                                            .responseCode(RC_200_000)
                                            .data(result)
                                            .build());
    }

    @Operation(summary = "Get list of user's favorite products", description = "Get all user's favorite products info")
    @GetMapping(value = "/favorite")
    public ResponseEntity<ApiResponse<List<UserProductDTO>, Object>> getFavoriteProduct() {
        log.info("Processing get user's favorited product ");
        List<UserProduct> productList = userProductService.getFavoriteProducts();
        var result = productList.stream()
                                .map(userProductMapper::toDto)
                                .toList();
        return ResponseEntity.ok(ApiResponse.<List<UserProductDTO>, Object>builder()
                                            .status(ResponseStatus.SUCCESS)
                                            .responseCode(RC_200_000)
                                            .data(result)
                                            .build());
    }


    @Operation(summary = "Update user's favorite list", description = "Add/Remove product to/from favorite list")
    @PutMapping(value = "/favorite")
    public ResponseEntity<ApiResponse<UserProductDTO, Object>> updateFavoriteProduct(@RequestBody ProductFavoriteRequest request) {
        log.info("Processing add/remove product to favorite list...");
        UserProduct userProduct = userProductService.favoriteProduct(request);
        var result = userProductMapper.toDto(userProduct);
        return ResponseEntity.ok(ApiResponse.<UserProductDTO, Object>builder()
                                            .status(ResponseStatus.SUCCESS)
                                            .responseCode(RC_200_000)
                                            .data(result)
                                            .build());
    }

}
