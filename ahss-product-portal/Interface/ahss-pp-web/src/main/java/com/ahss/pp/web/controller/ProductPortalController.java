package com.ahss.pp.web.controller;

import static com.ahss.common.api.AHSSResponseCode.RC_200_000;

import com.ahss.common.api.ApiResponse;
import com.ahss.common.api.ResponseStatus;
import com.ahss.pp.core.service.ProductService;
import com.ahss.pp.db.model.Product;
import com.ahss.pp.dm.dto.ProductDTO;
import com.ahss.pp.dm.mapper.ProductMapper;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping(value = "/v1/api/pp")
@Validated
@Hidden
@Tag(name = "Product", description = "Product Management")
public class ProductPortalController {

    private final ProductService productService;
    private final ProductMapper productMapper;

    @Operation(summary = "Get list of products", description = "Get all products' info")
    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200",
        description = "Successfully retrieved item",
        content = @Content(mediaType = "application/json",
            schema = @Schema(implementation = ApiResponse.class),
            examples = @ExampleObject(name = "Successful retrieve product list", value = """
                {
                  "status": "SUCCESS",
                  "responseCode": {
                    "code": 200000,
                    "message": "Success",
                    "detailMessage": "Request has been processed successfully"
                  },
                  "data": {
                    "id": 1,
                    "productName": "Insights Studio"
                  },
                  "request": null,
                  "actions": null
                }
                """)))
    @GetMapping(value = "/products")
    public ResponseEntity<ApiResponse<List<ProductDTO>, Object>> getProduct() {
        log.info("Processing product retrieval request...");
        List<Product> productList = productService.getAllProducts();
        var result = productList.stream()
                                .map(productMapper::toDto)
                                .toList();
        return ResponseEntity.ok(ApiResponse.<List<ProductDTO>, Object>builder()
                                            .status(ResponseStatus.SUCCESS)
                                            .responseCode(RC_200_000)
                                            .data(result)
                                            .build());
    }

    @PostMapping(value = "/products")
    @Operation(summary = "Create a product", description = "Create Product")
    public ResponseEntity<ApiResponse<ProductDTO, Object>> createProduct(@Valid @RequestBody ProductDTO createProductRequest) {
        log.info("Processing create product request...");
        ProductDTO product = productMapper.toDto(productService.createProduct(createProductRequest));
        return ResponseEntity.ok(ApiResponse.<ProductDTO, Object>builder()
                                            .status(ResponseStatus.SUCCESS)
                                            .responseCode(RC_200_000)
                                            .data(product)
                                            .build());
    }

    @PutMapping(value = "/products")
    @Operation(summary = "Update a product", description = "Update Product")
    public ResponseEntity<ApiResponse<ProductDTO, Object>> updateProduct(@Valid @RequestBody ProductDTO createProductRequest) {
        log.info("Processing update product request...");
        ProductDTO product = productMapper.toDto(productService.updateProduct(createProductRequest));
        return ResponseEntity.ok(ApiResponse.<ProductDTO, Object>builder()
                                            .status(ResponseStatus.SUCCESS)
                                            .responseCode(RC_200_000)
                                            .data(product)
                                            .build());
    }

    @DeleteMapping(value = "/products/{id}")
    @Operation(summary = "Remove a product", description = "Remove Product")
    public ResponseEntity<ApiResponse<Integer, Object>> deleteProduct(@PathVariable Integer id) {
        log.info("Processing product deletion :" + id);
        productService.deleteProduct(id);
        return ResponseEntity.ok(ApiResponse.<Integer, Object>builder()
                                            .status(ResponseStatus.SUCCESS)
                                            .responseCode(RC_200_000)
                                            .data(id)
                                            .build());
    }

    @GetMapping(value = "/products/{appId}")
    @Operation(summary = "Get product", description = "Get Product by Id")
    public ResponseEntity<ApiResponse<ProductDTO, Object>> getProduct(@PathVariable String appId, @RequestParam List<String> tenantCodes) {
        Product product = productService.getProductByAppIdAndTenantCodes(appId, tenantCodes);
        return ResponseEntity.ok(ApiResponse.<ProductDTO, Object>builder()
                                            .status(ResponseStatus.SUCCESS)
                                            .responseCode(RC_200_000)
                                            .data(productMapper.toDto(product))
                                            .build());
    }

}
