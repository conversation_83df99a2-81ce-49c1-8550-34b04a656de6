package com.ahss.pp;

import com.microsoft.applicationinsights.attach.ApplicationInsights;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication(scanBasePackages = {"com.ahss.pp.config", "com.ahss.pp.web", "com.ahss.pp.core", "com.ahss.pp.dm", "com.ahss.common.config",
    "com.ahss.common.utils", "com.ahss.pp.db.repository"})
@EntityScan(basePackages = "com.ahss.pp.db.model")
@EnableAsync
public class AHProductPortalApplication {

    public static void main(String[] args) {
        ApplicationInsights.attach();
        SpringApplication.run(AHProductPortalApplication.class, args);
    }
}
