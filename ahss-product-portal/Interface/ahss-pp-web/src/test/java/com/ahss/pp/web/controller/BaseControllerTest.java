package com.ahss.pp.web.controller;

import com.ahss.pp.core.service.ProductService;
import com.ahss.pp.db.repository.ProductConfigRepository;
import com.ahss.pp.db.repository.ProductRepository;
import com.ahss.pp.db.repository.UserProductRepository;
import jakarta.persistence.EntityManager;
import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration;
import org.springframework.boot.autoconfigure.jooq.JooqAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.data.jpa.mapping.JpaMetamodelMappingContext;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@EnableAutoConfiguration(exclude = {FlywayAutoConfiguration.class, JooqAutoConfiguration.class,
    HibernateJpaAutoConfiguration.class})
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = {"ahpp.swagger.show-hidden=true"})
@MockBean(JpaMetamodelMappingContext.class)
public abstract class BaseControllerTest {


    @LocalServerPort
    protected int port;

    @MockBean
    protected DataSource dataSource;

    @MockBean
    protected EntityManager entityManager;

    @MockBean
    protected ProductRepository productRepository;

    @MockBean
    protected ProductConfigRepository productConfigRepository;

    @MockBean
    protected UserProductRepository userProductRepository;

    @Autowired
    protected ProductService productService;

    @Autowired
    protected MockMvc mockMvc;

    @MockBean
    private JwtDecoder jwtDecoder;
}
