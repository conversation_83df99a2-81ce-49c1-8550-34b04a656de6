package com.ahss.pp.web.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.ahss.common.domain.context.UserContext;
import com.ahss.common.domain.context.UserContextHolder;
import com.ahss.pp.db.model.Product;
import com.ahss.pp.db.model.UserProduct;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.http.MediaType;


class UserProductControllerTest extends BaseControllerTest {

    private static final String BASE_URL = "/v1/api/pp/user-product";

    private UserContext userContext;

    @BeforeEach
    void setUp() {
        userContext = UserContext.builder().sub("user123").build();
        UserContextHolder.setUserContext(userContext);
    }

    private UserProduct mockUserProduct(String uid, Integer productId, boolean isFavorite) {
        return UserProduct.builder()
                          .uid(uid)
                          .product(Product.builder().productId(productId).build())
                          .isFavorite(isFavorite)
                          .build();
    }

    @Test
    void getUserProducts() throws Exception {
        var mockUserProductList = List.of(mockUserProduct("1", 1, true));
        given(userProductRepository.findByUidAndIsFavorite(eq(userContext.getSub()), eq(true))).willReturn(mockUserProductList);

        mockMvc.perform(get("http://localhost:" + port + BASE_URL + "/favorite")
                            .contentType(MediaType.APPLICATION_JSON))
               .andExpect(status().isOk())
               .andExpect(jsonPath("$.data[0].isFavorite").value(true));
    }

    @Test
    void createUserProduct() throws Exception {
        var newUserProduct = mockUserProduct("1", 1, true);
        given(userProductRepository.findFirstByProduct_ProductIdAndUid(any(), any())).willReturn(Optional.empty());
        given(productRepository.findProductByTenantCodesAndId(any(), eq(1))).willReturn(Optional.of(Product.builder().productId(1).build()));
        given(userProductRepository.save(any(UserProduct.class))).willReturn(newUserProduct);

        mockMvc.perform(put("http://localhost:" + port + BASE_URL + "/favorite")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content("{\"isFavorite\": true, \"productId\": 1}"))
               .andExpect(status().isOk())
               .andExpect(jsonPath("$.data.isFavorite").value(true));

        verify(userProductRepository, times(1)).save(any(UserProduct.class));
    }

    @Test
    void updateUserProduct() throws Exception {
        var newUserProduct = mockUserProduct("1", 1, true);
        given(userProductRepository.findFirstByProduct_ProductIdAndUid(any(), any())).willReturn(Optional.of(newUserProduct));
        given(productRepository.findProductByTenantCodesAndId(any(), eq(1))).willReturn(Optional.of(Product.builder().productId(1).build()));
        given(userProductRepository.save(any(UserProduct.class))).willReturn(newUserProduct);

        mockMvc.perform(put("http://localhost:" + port + BASE_URL + "/favorite")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content("{\"isFavorite\": false, \"productId\": 1}"))
               .andExpect(status().isOk())
               .andExpect(jsonPath("$.data.isFavorite").value(false));

        ArgumentCaptor<UserProduct> userProductCaptor = ArgumentCaptor.forClass(UserProduct.class);
        verify(userProductRepository, times(1)).save(userProductCaptor.capture());

        UserProduct savedUserProduct = userProductCaptor.getValue();
        assertEquals(false, savedUserProduct.getIsFavorite()); // Check that isFavorite is false
        assertEquals("user123", savedUserProduct.getUid());   // Check that the uid is correct
    }

}
