package com.ahss.pp.web.controller;

import static com.ahss.common.api.AHSSResponseCode.RC_200_000;
import static com.ahss.common.api.AHSSResponseCode.RC_404_000;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.ahss.common.api.ResponseStatus;
import com.ahss.pp.db.model.Product;
import com.ahss.pp.dm.dto.ProductDTO;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;

class ProductPortalControllerTest extends BaseControllerTest {

    private static final String BASE_URL = "/v1/api/pp";

    private Product mockProduct(Integer id) {
        return Product.builder()
                      .productId(id)
                      .productName("Test Product " + id)
                      .productCode("PCODE " + id)
                      .description("Description " + id)
                      .build();
    }

    private ProductDTO mockProductDTO(Integer id) {
        return ProductDTO.builder()
                         .productId(id)
                         .productName("Test Product " + id)
                         .productCode("PCODE " + id)
                         .description("Description " + id)
                         .build();
    }

    @Test
    void getProduct() throws Exception {
        // Given
        var mockProductList = List.of(
            Product.builder().productId(1).productName("Insights Studio").description("Precision Insights Studio").build());
        given(productRepository.findAll()).willReturn(mockProductList);

        // When & Then
        this.mockMvc.perform(get("http://localhost:" + port + BASE_URL + "/products")).andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.status").value(ResponseStatus.SUCCESS.name()))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_200_000.getCode()))
                    .andExpect(jsonPath("$.data[0].productName").value("Insights Studio"));
    }


    @Test
    void createProduct() throws Exception {
        Product product = mockProduct(null);

        Product newProduct = mockProduct(1);

        given(productRepository.save(eq(product))).willReturn(newProduct);

        this.mockMvc.perform(post("http://localhost:" + port + BASE_URL + "/products")
                                 .contentType(MediaType.APPLICATION_JSON)
                                 .content("{\"productName\": \"New Product\"}"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.status").value(ResponseStatus.SUCCESS.name()))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_200_000.getCode()))
                    .andExpect(jsonPath("$.data.productName").value(newProduct.getProductName()))
                    .andExpect(jsonPath("$.data.productId").value(newProduct.getProductId()));

        verify(productRepository, times(1)).save(product);
    }

    @Test
    void updateProduct() throws Exception {
        int productId = 1;
        Product product = mockProduct(productId);

        given(productRepository.findById(eq(productId))).willReturn(Optional.of(product));
        given(productRepository.save(eq(product))).willReturn(product);
        given(productConfigRepository.saveAll(eq(product.getProductConfigs()))).willReturn(product.getProductConfigs());

        this.mockMvc.perform(put("http://localhost:" + port + BASE_URL + "/products")
                                 .contentType(MediaType.APPLICATION_JSON)
                                 .content("{\"productId\": 1, \"productName\": \"Updated Product\"}"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.status").value(ResponseStatus.SUCCESS.name()))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_200_000.getCode()))
                    .andExpect(jsonPath("$.data.productName").value("Updated Product"));
        verify(productRepository, times(1)).save(product);
        verify(productConfigRepository, times(1)).saveAll(any());
    }

    @Test
    void updateProductNotFound() throws Exception {
        int productId = 1;

        given(productRepository.findById(eq(productId))).willReturn(Optional.empty());

        this.mockMvc.perform(put("http://localhost:" + port + BASE_URL + "/products")
                                 .contentType(MediaType.APPLICATION_JSON)
                                 .content("{\"productId\": 1, \"productName\": \"Updated Product\"}"))
                    .andExpect(status().isNotFound())
                    .andExpect(jsonPath("$.status").value(ResponseStatus.FAILED.name()))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_404_000.getCode()));
    }

    @Test
    void deleteProduct() throws Exception {
        int productId = 1;
        Product product = mockProduct(productId);
        given(productRepository.findById(eq(productId))).willReturn(Optional.of(product)); // Mock findById
        doNothing().when(productRepository).delete(eq(product));
        this.mockMvc.perform(delete("http://localhost:" + port + BASE_URL + "/products/1"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.status").value(ResponseStatus.SUCCESS.name()))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_200_000.getCode()))
                    .andExpect(jsonPath("$.data").value(productId));
        verify(productRepository, times(1)).delete(product);
    }

    @Test
    void deleteProductNotFound() throws Exception {
        Product product = mockProduct(1);
        given(productRepository.findById(eq(product.getProductId()))).willReturn(Optional.empty());

        this.mockMvc.perform(delete("http://localhost:" + port + BASE_URL + "/products/1"))
                    .andExpect(status().isNotFound())
                    .andExpect(jsonPath("$.status").value(ResponseStatus.FAILED.name()))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_404_000.getCode()));
    }

    @Test
    void getProductById() throws Exception {
        // Given
        Product product = mockProduct(1);

        given(productRepository.findProductByProductCodeAndTenantCodes(eq("AHIS"), argThat(
            tenantCodes -> tenantCodes != null && tenantCodes.contains("AIA_SG")))).willReturn(Optional.ofNullable(product));

        // When & Then
        this.mockMvc.perform(get("http://localhost:" + port + BASE_URL + "/products/AHIS?tenantCodes=AIA_SG"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.status").value(ResponseStatus.SUCCESS.name()))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_200_000.getCode()))
                    .andExpect(jsonPath("$.data.productName").value(product.getProductName()));
    }

    @Test
    void getProductByAppIdNotFound() throws Exception {
        // Given
        Product product = mockProduct(1);
        given(productRepository.findProductByProductCodeAndTenantCodes(any(), anyList())).willReturn(Optional.empty());

        // When & Then
        this.mockMvc.perform(get("http://localhost:" + port + BASE_URL + "/products/AHIS?tenantCodes=AIA_SG"))
                    .andExpect(status().isNotFound())
                    .andExpect(jsonPath("$.status").value(ResponseStatus.FAILED.name()))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_404_000.getCode()));
    }
}
