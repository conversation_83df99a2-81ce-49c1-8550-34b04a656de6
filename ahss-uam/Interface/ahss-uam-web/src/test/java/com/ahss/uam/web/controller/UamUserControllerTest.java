package com.ahss.uam.web.controller;

import static com.ahss.common.api.AHSSResponseCode.RC_200_000;
import static com.ahss.common.api.AHSSResponseCode.RC_400_002;
import static com.ahss.common.api.AHSSResponseCode.RC_404_001;
import static com.ahss.common.api.AHSSResponseCode.RC_404_003;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.ahss.common.api.AHSSResponseCode;
import com.ahss.common.api.ResponseStatus;
import com.ahss.common.domain.context.UserContext;
import com.ahss.common.domain.context.UserContextHolder;
import com.ahss.uam.core.client.ProductPortalClient;
import com.ahss.uam.core.service.hmac.HMACService;
import com.ahss.uam.core.service.hmac.HMACServiceFactory;
import com.ahss.uam.db.model.StateCache;
import com.ahss.uam.db.model.Tenant;
import com.ahss.uam.db.model.UserEntity;
import com.ahss.uam.db.model.enums.UserStatus;
import com.ahss.uam.dm.dto.ProductRedirectUrlDTO;
import com.ahss.uam.dm.dto.StateTokenDTO;
import com.ahss.uam.dm.dto.product.ProductConfigDTO;
import com.ahss.uam.dm.dto.product.ProductDTO;
import com.ahss.uam.dm.dto.product.enums.ProductConfigType;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper;

class UamUserControllerTest extends BaseControllerTest {

    private static final String BASE_URL = "/v1/api/uam/user";

    private final ObjectMapper objectMapper = new ObjectMapper();

    @MockBean
    private HMACServiceFactory hmacServiceFactory;

    @MockBean
    private ProductPortalClient productPortalClient;

    @Test
    void searchUsers() throws Exception {
        // Given
        when(userEntityRepository.searchUsers(any(), any(), any(), any(), any(), any())).thenReturn(List.of(
            UserEntity.builder()
                      .entityId(1L)
                      .name("Test User")
                      .status(UserStatus.ACTIVE)
                      .build()));

        // When & Then
        this.mockMvc.perform(get("http://localhost:" + port + BASE_URL + "/search")).andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.status").value("SUCCESS"))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_200_000.getCode()))
                    .andExpect(jsonPath("$.data").isNotEmpty())
                    .andExpect(jsonPath("$.data[0].entityId").value(1))
                    .andExpect(jsonPath("$.data[0].name").value("Test User"));
    }

    @Test
    void createUserSuccess() throws Exception {
        UserEntity user = new UserEntity();
        user.setEntityId(1L);
        Tenant tenant = new Tenant();
        tenant.setTenantId(1);
        when(userEntityRepository.findById(eq(1L))).thenReturn(Optional.of(user));
        when(tenantRepository.findByTenantId(eq(1))).thenReturn(Optional.of(tenant));
        this.mockMvc.perform(post("http://localhost:" + port + BASE_URL)
                                 .contentType(MediaType.APPLICATION_JSON)
                                 .content("""
                                              {
                                                "name": "Test User",
                                                "tenantId": 1,
                                                "status": "ACTIVE",
                                                "path": "test/domain"
                                              }
                                              """))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.status").value("SUCCESS"));
    }

    @Test
    void createUser_BadRequestMissingTenant() throws Exception {
        UserEntity user = new UserEntity();
        user.setEntityId(1L);
        when(userEntityRepository.findById(eq(1L))).thenReturn(Optional.of(user));
        this.mockMvc.perform(post("http://localhost:" + port + BASE_URL)
                                 .contentType(MediaType.APPLICATION_JSON)
                                 .content("""
                                              {
                                                "name": "Test User",
                                                "status": "ACTIVE",
                                                "path": "test/domain"
                                              }
                                              """))
                    .andDo(print())
                    .andExpect(status().isBadRequest())
                    .andExpect(jsonPath("$.status").value("FAILED"))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_400_002.getCode()));
    }

    @Test
    void updateUserSuccess() throws Exception {
        UserEntity user = new UserEntity();
        user.setEntityId(1L);
        when(userEntityRepository.findById(eq(1L))).thenReturn(Optional.of(user));
        when(tenantRepository.findByTenantId(eq(1))).thenReturn(Optional.of(new Tenant()));
        this.mockMvc.perform(put("http://localhost:" + port + BASE_URL)
                                 .contentType(MediaType.APPLICATION_JSON)
                                 .content("""
                                              {
                                                "entityId": 1,
                                                "name": "Test User",
                                                "tenantId": 1
                                              }
                                              """))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.status").value("SUCCESS"));
    }

    @Test
    void updateUser_ThrowsNotFoundException() throws Exception {
        UserEntity user = new UserEntity();
        user.setEntityId(1L);
        Tenant tenant = new Tenant();
        tenant.setTenantId(1);
        when(userEntityRepository.findById(eq(1L))).thenReturn(Optional.of(user));
        when(tenantRepository.findById(eq(1))).thenReturn(Optional.empty());
        this.mockMvc.perform(put("http://localhost:" + port + BASE_URL)
                                 .contentType(MediaType.APPLICATION_JSON)
                                 .content("""
                                              {
                                                "entityId": 1,
                                                "name": "Test User",
                                                "tenantId": 1
                                              }
                                              """))
                    .andDo(print())
                    .andExpect(status().isNotFound())
                    .andExpect(jsonPath("$.status").value("FAILED"))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_404_003.getCode()));
    }

    @Test
    void deleteUser_Success() throws Exception {
        UserEntity user = new UserEntity();
        when(userEntityRepository.findById(eq(1L))).thenReturn(Optional.of(user));
        doNothing().when(userEntityRepository).delete(any());

        // Perform DELETE request
        this.mockMvc.perform(delete("http://localhost:" + port + BASE_URL + "/{id}", 1))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.status").value("SUCCESS"))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_200_000.getCode()))
                    .andExpect(jsonPath("$.data").value(1));

        verify(userEntityRepository, times(1)).delete(any());
    }

    @Test
    void deleteUser_ThrowsResourceNotFoundException() throws Exception {
        when(userEntityRepository.findById(eq(1L))).thenReturn(Optional.empty());

        this.mockMvc.perform(delete("http://localhost:" + port + BASE_URL + "/{id}", 1))
                    .andDo(print())
                    .andExpect(status().isNotFound())
                    .andExpect(jsonPath("$.status").value("FAILED"))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_404_001.getCode()));
    }

    @Test
    void getUser_Success() throws Exception {
        UserEntity user = new UserEntity();
        user.setEntityId(1L);
        user.setName("Test User");
        when(userEntityRepository.findById(eq(1L))).thenReturn(Optional.of(user));

        this.mockMvc.perform(get("http://localhost:" + port + BASE_URL + "/{id}", 1))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.status").value("SUCCESS"))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_200_000.getCode()))
                    .andExpect(jsonPath("$.data.entityId").value(1))
                    .andExpect(jsonPath("$.data.name").value("Test User"));
    }

    @WithMockUser(authorities = {"USER", "AHIS_AIA_HK_USER"})
    @Test
    void testGetRedirectUrl() throws Exception {
        String appId = "app-id";
        String requestRedirectUrl = "https://sub.product.url";
        ProductRedirectUrlDTO request = ProductRedirectUrlDTO
            .builder()
            .redirectUrl(requestRedirectUrl)
            .build();

        String accessToken = "access-token";
        UserContext userContext = UserContext
            .builder()
            .accessToken(accessToken)
            .build();
        UserContextHolder.setUserContext(userContext);

        HMACService hmacService = mock(HMACService.class);
        String expectedState = "expected-state";
        String expectedSignature = "expected-signature";
        String expectedTimestamp = "expected-timestamp";
        StateTokenDTO stateTokenDTO = StateTokenDTO
            .builder()
            .state(expectedState)
            .signature(expectedSignature)
            .timestamp(expectedTimestamp)
            .build();
        when(hmacServiceFactory.getHMACService(appId)).thenReturn(hmacService);
        when(hmacService.generateHMAC(anyString())).thenReturn(stateTokenDTO);

        String productUrl = "http://product.url";
        ProductDTO productDTO = ProductDTO
            .builder()
            .productConfigs(List.of(ProductConfigDTO
                                        .builder()
                                        .configType(ProductConfigType.URL)
                                        .configValue(productUrl)
                                        .build()))
            .build();
        when(productPortalClient.getProductFromPPService(eq(appId), anyList(), eq(accessToken))).thenReturn(productDTO);

        String expectedRedirectUrl =
            requestRedirectUrl
                + "?state="
                + expectedState
                + "&timestamp="
                + expectedTimestamp
                + "&signature="
                + expectedSignature
                + "&appId"
                + "="
                + appId;

        this.mockMvc
            .perform(post("http://localhost:" + port + BASE_URL + "/generate-state-token/{appId}", appId)
                         .contentType(MediaType.APPLICATION_JSON)
                         .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andDo(print())
            .andExpect(jsonPath("$.status").value(ResponseStatus.SUCCESS.name()))
            .andExpect(jsonPath("$.responseCode.code").value(RC_200_000.getCode()))
            .andExpect(jsonPath("$.data").value(expectedRedirectUrl));
    }

    @WithMockUser(authorities = "ADMIN")
    @Test
    void testValidateStateToken() throws Exception {
        // Arrange
        StateTokenDTO stateTokenDTO = new StateTokenDTO();
        stateTokenDTO.setState("valid-token");

        String expectedAccessToken = "access-token";
        when(stateCacheRepository.findByStateAndUsedAndExpiresAtGreaterThan(eq(stateTokenDTO.getState()), eq(false), any()))
            .thenReturn(Optional.of(StateCache.builder()
                                              .state(stateTokenDTO.getState())
                                              .accessToken(expectedAccessToken)
                                              .used(false)
                                              .build()));

        this.mockMvc.perform(post("http://localhost:" + port + BASE_URL + "/validate-state-token", 1)
                                 .contentType(MediaType.APPLICATION_JSON)
                                 .content("""
                                              {
                                                "state": "valid-token"
                                              }
                                              """))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.status").value(ResponseStatus.SUCCESS.name()))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_200_000.getCode()))
                    .andExpect(jsonPath("$.data.accessToken").value(expectedAccessToken));
    }

}
