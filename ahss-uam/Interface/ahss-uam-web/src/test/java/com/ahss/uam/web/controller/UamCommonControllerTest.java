package com.ahss.uam.web.controller;

import static com.ahss.common.api.AHSSResponseCode.RC_200_000;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import org.junit.jupiter.api.Test;


class UamCommonControllerTest extends BaseControllerTest {

    private static final String BASE_URL = "/v1/api/uam";

    @Test
    void hello() throws Exception {
        // Given

        // When & Then
        this.mockMvc.perform(get("http://localhost:" + port + BASE_URL + "/hello")).andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.status").value("SUCCESS"))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_200_000.getCode()))
                    .andExpect(jsonPath("$.data").value("Hello World"));

    }
}
