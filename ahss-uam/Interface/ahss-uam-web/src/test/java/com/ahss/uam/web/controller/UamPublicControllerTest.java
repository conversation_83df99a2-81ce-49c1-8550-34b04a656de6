package com.ahss.uam.web.controller;

import static com.ahss.common.api.AHSSResponseCode.RC_200_000;
import static com.ahss.common.api.AHSSResponseCode.RC_404_004;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.ahss.uam.db.model.SsoProvider;
import com.ahss.uam.db.model.enums.TenantStatus;
import java.util.List;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class UamPublicControllerTest extends BaseControllerTest {

    private static final String BASE_URL = "/v1/api/uam/public";

    @Test
    @Order(1)
    void getSSOUrl() throws Exception {
        // Given
        when(ssoProviderRepository.findByClientDomainAndTenant_TenantStatus(eq("test.com"), eq(TenantStatus.ACTIVE))).thenReturn(
            (List.of(SsoProvider.builder().clientDomain("test.com").discoveryUrl("http://test.com").build())));
        // When & Then
        this.mockMvc.perform(get("http://localhost:" + port + BASE_URL + "/sso-url?email=<EMAIL>"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.status").value("SUCCESS"))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_200_000.getCode()))
                    .andExpect(jsonPath("$.data").isNotEmpty())
                    .andExpect(jsonPath("$.data.ssoUrl").value("http://test.com"));
    }

    @Test
    void getSSOUrl_NotFound() throws Exception {
        // Given
        when(ssoProviderRepository.findByClientDomainAndTenant_TenantStatus(eq("test2.com"), eq(TenantStatus.ACTIVE))).thenReturn(List.of());
        // When & Then
        this.mockMvc
            .perform(get("http://localhost:" + port + BASE_URL + "/sso-url?email=<EMAIL>"))
                    .andExpect(status().isNotFound())
                    .andExpect(jsonPath("$.status").value("FAILED"))
                    .andExpect(jsonPath("$.responseCode.code").value(RC_404_004.getCode()));
    }

    @Test
    @Order(2)
    void getSSOUrl_ShouldbeCached() throws Exception {
        // Given
        when(ssoProviderRepository.findByClientDomainAndTenant_TenantStatus(eq("test.com"), eq(TenantStatus.ACTIVE))).thenReturn(List.of());
        // When & Then
        this.mockMvc
            .perform(get("http://localhost:" + port + BASE_URL + "/sso-url?email=<EMAIL>"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data").isNotEmpty())
            .andExpect(jsonPath("$.data.ssoUrl").value("http://test.com"));
    }
}
