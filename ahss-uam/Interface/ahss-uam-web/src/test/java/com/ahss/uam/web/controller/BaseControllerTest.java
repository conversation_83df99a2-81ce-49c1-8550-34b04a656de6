package com.ahss.uam.web.controller;

import com.ahss.uam.config.JwtDecoderFactory;
import com.ahss.uam.core.service.AESEncryptionService;
import com.ahss.uam.core.service.UamAdminTokenService;
import com.ahss.uam.core.service.UamEntityMappingService;
import com.ahss.uam.core.service.UamOrganizationService;
import com.ahss.uam.core.service.UamPublicService;
import com.ahss.uam.core.service.UamTenantService;
import com.ahss.uam.core.service.UamUserService;
import com.ahss.uam.db.repository.ClientConfigRepository;
import com.ahss.uam.db.repository.EntityMappingRepository;
import com.ahss.uam.db.repository.OrganizationRepository;
import com.ahss.uam.db.repository.ProfileRepository;
import com.ahss.uam.db.repository.SsoProviderRepository;
import com.ahss.uam.db.repository.StateCacheRepository;
import com.ahss.uam.db.repository.TenantConfigRepository;
import com.ahss.uam.db.repository.TenantRepository;
import com.ahss.uam.db.repository.UserEntityRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration;
import org.springframework.boot.autoconfigure.jooq.JooqAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.data.jpa.mapping.JpaMetamodelMappingContext;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@EnableAutoConfiguration(exclude = {FlywayAutoConfiguration.class, JooqAutoConfiguration.class,
    HibernateJpaAutoConfiguration.class})
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = {"ahpp.swagger.show-hidden=true"})
@MockBean(classes = {JpaMetamodelMappingContext.class, EntityManagerFactory.class, EntityManager.class, JwtDecoderFactory.class})
public abstract class BaseControllerTest {

  @LocalServerPort
  protected int port;

  @MockBean
  protected DataSource dataSource;

  @Autowired
  protected MockMvc mockMvc;

  // Mock Repository
  @MockBean
  protected TenantRepository tenantRepository;

  @MockBean
  protected OrganizationRepository organizationRepository;

  @MockBean
  protected UserEntityRepository userEntityRepository;

  @MockBean
  protected EntityMappingRepository entityMappingRepository;

  @MockBean
  protected SsoProviderRepository ssoProviderRepository;

  @MockBean
  protected StateCacheRepository stateCacheRepository;

  @MockBean
  protected TenantConfigRepository tenantConfigRepository;

  @MockBean
  protected ClientConfigRepository clientConfigRepository;

  @MockBean
  protected ProfileRepository profileRepository;

  @MockBean
  private JwtDecoder jwtDecoder;

  // Autowired Services
  @Autowired
  protected UamEntityMappingService uamEntityMappingService;

  @Autowired
  protected UamOrganizationService uamOrganizationService;

  @Autowired
  protected UamTenantService uamTenantService;

  @Autowired
  protected UamUserService uamUserService;

  @Autowired
  protected UamPublicService uamPublicService;

  @Autowired
  protected UamAdminTokenService uamAdminTokenService;

  @Autowired
  protected AESEncryptionService aesEncryptionService;


}
