package com.ahss.uam.web.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.ahss.uam.db.model.ClientConfig;
import com.ahss.uam.db.model.SsoProvider;
import com.ahss.uam.db.model.Tenant;
import com.ahss.uam.db.model.enums.ClientConfigStatus;
import com.ahss.uam.dm.dto.ClientConfigDTO;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;

class UamAdminControllerTest extends BaseControllerTest {

    private static final String BASE_URL = "/v1/api/admin/client-config";

    private static final String SSO_URL = "/v1/api/admin/sso-provider";

    private ClientConfigDTO clientConfigDTO;

    @BeforeEach
    void setUp() {
        clientConfigDTO = ClientConfigDTO.builder()
                                         .clientId("test-client")
                                         .clientSharedKey("test-shared-key")
                                         .build();
    }

    @Test
    void createServerToken() throws Exception {
        ClientConfig clientConfig = ClientConfig.builder().build();
        when(clientConfigRepository.findByClientSharedKeyAndClientAccessTokenAndStatus(any(),
                                                                                       any(),
                                                                                       eq(ClientConfigStatus.ACTIVE))).thenReturn(Optional.of(
            clientConfig));

        this.mockMvc.perform(post("http://localhost:" + port + BASE_URL)
                                 .contentType(MediaType.APPLICATION_JSON)
                                 .content("""
                                              {
                                                "clientId": "test-client",
                                                "clientSharedKey": "test-shared-key"
                                              }
                                              """)
                                 .accept(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk());
    }

    @Test
    void regenerateToken() throws Exception {
        ClientConfig clientConfig = ClientConfig.builder().build();
        clientConfig.setClientSecretKey(aesEncryptionService.encrypt(UUID.randomUUID().toString()));
        clientConfig.setClientId("test-client");
        clientConfig.setClientSharedKey("test-shared-key");
        clientConfig.setClientAccessToken("test-token");
        when(clientConfigRepository.findByClientIdAndClientSharedKey(any(), any())).thenReturn(Optional.of(clientConfig));
        this.mockMvc.perform(put("http://localhost:" + port + BASE_URL)
                                 .contentType(MediaType.APPLICATION_JSON)
                                 .content("""
                                              {
                                                "clientId": "test-client",
                                                "clientSharedKey": "test-shared-key"
                                              }
                                              """)
                                 .accept(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk());
    }

    @Test
    void createSsoUrl() throws Exception {
        Tenant tenant = new Tenant();
        tenant.setTenantCode("tenant1");
        SsoProvider existingSsoProvider = new SsoProvider();
        when(tenantRepository.findByTenantCode("tenant1")).thenReturn(Optional.of(tenant));
        when(ssoProviderRepository.findByTenantCode("tenant1")).thenReturn(Optional.of(existingSsoProvider));
        when(ssoProviderRepository.save(existingSsoProvider)).thenReturn(existingSsoProvider);

        this.mockMvc
            .perform(post("http://localhost:" + port + SSO_URL)
                         .contentType(MediaType.APPLICATION_JSON)
                         .content("""
                                      {
                                         "name": "AIA SSO URL",
                                         "discoveryUrl": "http://test.com",
                                         "clientId": "Client123",
                                         "clientSecret": "secret",
                                         "clientDomain": "aia.com",
                                         "tenantCode": "tenant1"
                                       }
                                      """)
                         .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data").isNotEmpty())
            .andExpect(jsonPath("$.data.ssoUrl").value("http://test.com"))
            .andExpect(jsonPath("$.data.clientId").value("Client123"));
        ;
    }


}
