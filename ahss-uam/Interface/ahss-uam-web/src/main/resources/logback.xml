<configuration>
  <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
  <springProperty scope="context" name="springAppName" source="spring.application.name"/>
  
  <!-- Container-friendly: Use /tmp for file logging if needed, but prefer console-only -->
  <property name="LOG_FILE" value="${LOG_FILE:-}"/>

  <property name="CONSOLE_LOG_PATTERN"
            value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr([${springAppName:-},%X{traceId:-},%X{spanId:-}]){faint} %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %clr(%M) %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>

  <!-- Primary appender: Console logging (container-friendly) -->
  <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
    <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
      <level>DEBUG</level>
    </filter>
    <encoder>
      <pattern>${CONSOLE_LOG_PATTERN}</pattern>
      <charset>utf8</charset>
    </encoder>
  </appender>

  <!-- JSON appender for structured logging (optional for containers) -->
  <appender name="JSON" class="ch.qos.logback.core.ConsoleAppender">
    <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
      <providers>
        <timestamp/>
        <logLevel/>
        <loggerName/>
        <message/>
        <mdc/>
        <context/>
        <arguments/>
        <stackTrace/>
        <customFields>{"springAppName":"${springAppName}"}</customFields>
        <custom>
          <keyValuePair>
            <key>traceId</key>
            <value>%X{traceId}</value>
          </keyValuePair>
          <keyValuePair>
            <key>spanId</key>
            <value>%X{spanId}</value>
          </keyValuePair>
        </custom>
      </providers>
    </encoder>
  </appender>

  <!-- Root logger: Console-only for container environments -->
  <root level="INFO">
    <appender-ref ref="CONSOLE"/>
    <!-- Uncomment for JSON structured logging in production containers -->
    <!-- <appender-ref ref="JSON"/> -->
  </root>

  <!-- Logger configurations -->
  <logger name="com.ahss" level="DEBUG"/>
  <logger name="org.hibernate.SQL" level="ERROR"/>
  <logger name="org.springframework.web" level="INFO"/>
</configuration>