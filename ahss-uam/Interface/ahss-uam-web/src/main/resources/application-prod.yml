server:
  port: 8011

spring:
  application:
    name: ah-uam-web
  cloud:
    azure:
      credential:
        managed-identity-enabled: true
        client-id: ${DB_CLIENT_ID:638c20cd-0046-43c6-a521-ff475cb0859c}
  datasource:
    url: ${DB_URL:*****************************************}
    driver-class-name: org.postgresql.Driver
    username: ${DB_USERNAME:ahss}
    password: ${DB_PASSWORD:password}
    azure:
      passwordless-enabled: ${PASSWORDLESS_ENABLED:false}
    hikari:
      maximum-pool-size: 10
  flyway:
    schemas:
      - uam
    connect-retries: 3
    locations:
      - classpath:db/migration/prod

okta:
  api-gateway:
    client-id: ${OKTA_API_GATEWAY_CLIENT_ID:0oao3tudxdZGOq0MW4x7}

security:
  jwt:
    issuers:
      - jwk-set-uri: ${JWK_SET_URI_WORKFORCE:https://aia.okta.com/oauth2/auso3tx5fwrMSxMqO4x7/v1/keys}
        issuer: ${ISSUER_URI_WORKFORCE:https://aia.okta.com/oauth2/auso3tx5fwrMSxMqO4x7}
        audience: ${JWT_AUDIENCE_WORKFORCE:Amplify Health Product Portal}
      - jwk-set-uri: ${JWK_SET_URI_CIAM:https://ah-prod.okta.com/oauth2/aus6g65022nDV0a373l7/v1/keys}
        issuer: ${ISSUER_URI_CIAM:https://ah-prod.okta.com/oauth2/aus6g65022nDV0a373l7}
        audience: ${JWT_AUDIENCE_CIAM:ah_server}
