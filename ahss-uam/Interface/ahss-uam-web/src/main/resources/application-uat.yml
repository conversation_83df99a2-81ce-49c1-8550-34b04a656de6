server:
  port: 8011

spring:
  flyway:
    schemas:
      - uam
    connect-retries: 3
    locations:
      - classpath:db/migration/uat

okta:
  api-gateway:
    client-id: ${OKTA_API_GATEWAY_CLIENT_ID:0oa1zsdldaxAOPqeW0h8}

security:
  jwt:
    issuers:
      - jwk-set-uri: ${JWK_SET_URI_WORKFORCE:https://aiatest.okta.com/oauth2/aus1zilsv95ktmdzW0h8/v1/keys}
        issuer: ${ISSUER_URI_WORKFORCE:https://aiatest.okta.com/oauth2/aus1zilsv95ktmdzW0h8}
        audience: ${JWT_AUDIENCE_WORKFORCE:Amplify Health Product Portal}
      - jwk-set-uri: ${JWK_SET_URI_CIAM:https://ah-uat.okta.com/oauth2/aus5ya2z0vzlB2Irf3l7/v1/keys}
        issuer: ${ISSUER_URI_CIAM:https://ah-uat.okta.com/oauth2/aus5ya2z0vzlB2Irf3l7}
        audience: ${JWT_AUDIENCE_CIAM:ah_server}
