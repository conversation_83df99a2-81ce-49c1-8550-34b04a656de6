package com.ahss.uam.web.controller;

import static com.ahss.common.api.AHSSResponseCode.RC_200_000;

import com.ahss.common.api.ApiResponse;
import com.ahss.common.api.ResponseStatus;
import com.ahss.uam.core.service.UamTenantService;
import com.ahss.uam.db.model.Tenant;
import com.ahss.uam.dm.dto.CreateTenantDTO;
import com.ahss.uam.dm.dto.TenantDTO;
import com.ahss.uam.dm.mapper.TenantMapper;
import io.micrometer.observation.annotation.Observed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/v1/api/uam/tenant")
@Tag(name = "Tenant", description = "Tenant Management")
@ConditionalOnProperty(name = "ahpp.swagger.show-hidden", havingValue = "true", matchIfMissing = true)
@Observed
public class UamTenantController {

    private final UamTenantService uamTenantService;
    private final TenantMapper tenantMapper;

    public UamTenantController(UamTenantService uamTenantService, TenantMapper tenantMapper) {
        this.uamTenantService = uamTenantService;
        this.tenantMapper = tenantMapper;
    }

    @GetMapping()
    public ResponseEntity<ApiResponse<List<TenantDTO>, Object>> getAllTenants() {
        var result = uamTenantService.getAllTenants();
        var dto = result.stream().map(tenantMapper::toDto).toList();
        return ResponseEntity.ok(
            ApiResponse.<List<TenantDTO>, Object>builder().status(ResponseStatus.SUCCESS).responseCode(RC_200_000).data(dto).build());
    }


    @PostMapping(value = "")
    @Operation(summary = "Create an tenant", description = "Create Tenant")
    public ResponseEntity<ApiResponse<CreateTenantDTO, Object>> createTenant(@Valid @RequestBody CreateTenantDTO tenantDTO) {
        CreateTenantDTO tenant = tenantMapper.toCreateDto(uamTenantService.createTenant(tenantDTO));
        return ResponseEntity.ok(ApiResponse.<CreateTenantDTO, Object>builder()
                                            .status(ResponseStatus.SUCCESS)
                                            .responseCode(RC_200_000)
                                            .data(tenant)
                                            .build());
    }

    @PutMapping(value = "")
    @Operation(summary = "Update an tenant", description = "Update Tenant")
    public ResponseEntity<ApiResponse<TenantDTO, Object>> updateTenant(@Valid @RequestBody TenantDTO tenantDTO) {
        TenantDTO tenant = tenantMapper.toDto(uamTenantService.updateTenant(tenantDTO));
        return ResponseEntity.ok(ApiResponse.<TenantDTO, Object>builder()
                                            .status(ResponseStatus.SUCCESS)
                                            .responseCode(RC_200_000)
                                            .data(tenant)
                                            .build());
    }

    @DeleteMapping(value = "/{id}")
    @Operation(summary = "Delete an tenant", description = "Delete Tenant")
    public ResponseEntity<ApiResponse<Integer, Object>> deleteTenant(@Valid @PathVariable Integer id) {
        uamTenantService.deleteTenant(id);
        return ResponseEntity.ok(ApiResponse.<Integer, Object>builder()
                                            .status(ResponseStatus.SUCCESS)
                                            .responseCode(RC_200_000)
                                            .data(id)
                                            .build());
    }

    @GetMapping(value = "/{id}")
    @Operation(summary = "Get an tenant", description = "Get Tenant's information")
    public ResponseEntity<ApiResponse<TenantDTO, Object>> getTenant(@Valid @PathVariable Integer id) {
        Tenant tenant = uamTenantService.getTenant(id);
        return ResponseEntity.ok(ApiResponse.<TenantDTO, Object>builder()
                                            .status(ResponseStatus.SUCCESS)
                                            .responseCode(RC_200_000)
                                            .data(tenantMapper.toDto(tenant))
                                            .build());
    }
}
