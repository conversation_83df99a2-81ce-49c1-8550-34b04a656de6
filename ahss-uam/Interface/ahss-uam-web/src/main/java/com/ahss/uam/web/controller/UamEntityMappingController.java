package com.ahss.uam.web.controller;

import static com.ahss.common.api.AHSSResponseCode.RC_200_000;

import com.ahss.common.api.ApiResponse;
import com.ahss.common.api.ResponseStatus;
import com.ahss.uam.core.service.UamEntityMappingService;
import com.ahss.uam.dm.dto.EntityMappingDTO;
import com.ahss.uam.dm.mapper.EntityMappingMapper;
import io.micrometer.observation.annotation.Observed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/v1/api/uam/user/{entityId}/entitymapping")
@Tag(name = "Entity Mapping", description = "Entity Mapping Management")
@ConditionalOnProperty(name = "ahpp.swagger.show-hidden", havingValue = "true", matchIfMissing = true)
@Observed
public class UamEntityMappingController {

    private final UamEntityMappingService uamEntityMappingService;
    private final EntityMappingMapper entityMappingMapper;

    public UamEntityMappingController(UamEntityMappingService uamEntityMappingService, EntityMappingMapper entityMappingMapper) {
        this.uamEntityMappingService = uamEntityMappingService;
        this.entityMappingMapper = entityMappingMapper;
    }

    @PostMapping(value = "")
    @Operation(summary = "Create entity mapping", description = "Add entity mapping to a user")
    public ResponseEntity<ApiResponse<EntityMappingDTO, Object>> createEntityMapping(@PathVariable Long entityId,
                                                                                     @Valid @RequestBody EntityMappingDTO entityMappingDTO) {
        EntityMappingDTO entityMapping = entityMappingMapper.toDto(uamEntityMappingService.createEntityMapping(entityId, entityMappingDTO));
        return ResponseEntity.ok(ApiResponse.<EntityMappingDTO, Object>builder()
                                            .status(ResponseStatus.SUCCESS)
                                            .responseCode(RC_200_000)
                                            .data(entityMapping)
                                            .build());
    }

    @PutMapping(value = "/{entityMappingId}")
    @Operation(summary = "Update an entity mapping", description = "Update entity mapping of a user")
    public ResponseEntity<ApiResponse<EntityMappingDTO, Object>> updateEntityMapping(@PathVariable Long entityId,
                                                                                     @PathVariable Long entityMappingId,
                                                                                     @Valid @RequestBody EntityMappingDTO entityMappingDTO) {
        EntityMappingDTO entityMappingMapperDto = entityMappingMapper.toDto(
            uamEntityMappingService.updateEntityMapping(entityId, entityMappingId, entityMappingDTO));
        return ResponseEntity.ok(ApiResponse.<EntityMappingDTO, Object>builder()
                                            .status(ResponseStatus.SUCCESS)
                                            .responseCode(RC_200_000)
                                            .data(entityMappingMapperDto)
                                            .build());
    }

    @PutMapping(value = "")
    @Operation(summary = "Update entity mappings", description = "Update all entity mappings of a user")
    public ResponseEntity<ApiResponse<List<EntityMappingDTO>, Object>> updateEntityMappings(@PathVariable Long entityId,
                                                                                            @Valid @RequestBody
                                                                                            List<EntityMappingDTO> entityMappingDTOList) {
        List<EntityMappingDTO> entityMappings = entityMappingMapper.toDtos(
            uamEntityMappingService.updateEntityMappings(entityId, entityMappingDTOList));
        return ResponseEntity.ok(ApiResponse.<List<EntityMappingDTO>, Object>builder()
                                            .status(ResponseStatus.SUCCESS)
                                            .responseCode(RC_200_000)
                                            .data(entityMappings)
                                            .build());
    }


    @DeleteMapping(value = "/{id}")
    @Operation(summary = "Remove user entityMapping", description = "Remove entityMapping from a user")
    public ResponseEntity<ApiResponse<Long, Object>> deleteEntityMapping(@PathVariable Long entityId, @PathVariable Long id) {
        uamEntityMappingService.deleteEntityMapping(id);
        return ResponseEntity.ok(ApiResponse.<Long, Object>builder()
                                            .status(ResponseStatus.SUCCESS)
                                            .responseCode(RC_200_000)
                                            .data(id)
                                            .build());
    }
}
