package com.ahss.uam.web.controller;

import static com.ahss.common.api.AHSSResponseCode.RC_200_000;

import com.ahss.common.api.ApiResponse;
import com.ahss.common.api.ResponseStatus;
import com.ahss.uam.core.service.UamAdminTokenService;
import com.ahss.uam.db.model.SsoProvider;
import com.ahss.uam.dm.dto.ClientConfigDTO;
import com.ahss.uam.dm.dto.CreateSsoProviderDTO;
import com.ahss.uam.dm.dto.ServerKeyDTO;
import com.ahss.uam.dm.dto.SsoProviderDTO;
import io.micrometer.observation.annotation.Observed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.websocket.server.PathParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/v1/api/admin")
@Tag(name = "Admin", description = "Admin Client Token Management")
@ConditionalOnProperty(name = "ahpp.swagger.show-hidden", havingValue = "true", matchIfMissing = true)
@Observed
public class UamAdminController {

    private final UamAdminTokenService uamAdminTokenService;

    public UamAdminController(UamAdminTokenService uamAdminTokenService) {
        this.uamAdminTokenService = uamAdminTokenService;
    }

    @PostMapping(value = "/client-config")
    @Operation(summary = "Generate Server Token", description = "Generate APIkey and Token for service to service communication")
    public ResponseEntity<ApiResponse<ServerKeyDTO, Object>> createServerToken(@RequestBody ClientConfigDTO clientConfigDTO) {
        ServerKeyDTO serverKeyDTO = uamAdminTokenService.createClientConfig(clientConfigDTO);
        return ResponseEntity.ok(
            ApiResponse.<ServerKeyDTO, Object>builder().status(ResponseStatus.SUCCESS).responseCode(RC_200_000).data(serverKeyDTO).build());
    }

    @PutMapping(value = "/client-config")
    @Operation(summary = "Generate Server Token", description = "Generate APIkey and Token for service to service communication")
    public ResponseEntity<ApiResponse<ServerKeyDTO, Object>> regenerateToken(@RequestBody ClientConfigDTO clientConfigDTO) {
        ServerKeyDTO serverKeyDTO = uamAdminTokenService.updateClientConfig(clientConfigDTO);
        return ResponseEntity.ok(
            ApiResponse.<ServerKeyDTO, Object>builder().status(ResponseStatus.SUCCESS).responseCode(RC_200_000).data(serverKeyDTO).build());
    }

    @GetMapping(value = "/client-config/{clientId}")
    @Operation(summary = "Get Shared key", description = "Get shared key by Client Id")
    public ResponseEntity<ApiResponse<ServerKeyDTO, Object>> getSharedKey(@PathParam("clientId") String clientId) {
        ServerKeyDTO serverKeyDTO = uamAdminTokenService.getApiKeyByClientId(clientId);
        return ResponseEntity.ok(
            ApiResponse.<ServerKeyDTO, Object>builder().status(ResponseStatus.SUCCESS).responseCode(RC_200_000).data(serverKeyDTO).build());
    }

    @DeleteMapping(value = "/client-config/{clientId}")
    @Operation(summary = "Delete Shared key", description = "Delete shared key by Client Id")
    public ResponseEntity<ApiResponse<ServerKeyDTO, Object>> deleteClientConfig(@PathParam("clientId") String clientId) {
        uamAdminTokenService.deleteClientConfig(clientId);
        return ResponseEntity.ok(
            ApiResponse.<ServerKeyDTO, Object>builder().status(ResponseStatus.SUCCESS).responseCode(RC_200_000).build());
    }

    @PostMapping(value = "/sso-provider")
    @Operation(summary = "Create Sso discovery url", description = "Create Sso discovery url")
    public ResponseEntity<ApiResponse<SsoProviderDTO, Object>> createSsoUrl(@RequestBody CreateSsoProviderDTO ssoProviderDTO) {
        SsoProvider ssoProvider = uamAdminTokenService.createSsoUrl(ssoProviderDTO);
        SsoProviderDTO ssoProviderDto = SsoProviderDTO
            .builder()
            .ssoUrl(ssoProvider.getDiscoveryUrl())
            .clientId(ssoProvider.getClientId())
            .build();
        return ResponseEntity.ok(
            ApiResponse
                .<SsoProviderDTO, Object>builder()
                .status(ResponseStatus.SUCCESS)
                .responseCode(RC_200_000)
                .data(ssoProviderDto)
                .build());
    }

}
