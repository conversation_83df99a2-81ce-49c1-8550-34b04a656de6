package com.ahss.uam.web.controller;

import static com.ahss.common.api.AHSSResponseCode.RC_200_000;

import com.ahss.common.api.ApiResponse;
import com.ahss.common.api.PageSupport;
import com.ahss.common.api.ResponseStatus;
import com.ahss.uam.core.service.UamUserService;
import com.ahss.uam.db.model.UserEntity;
import com.ahss.uam.dm.dto.AccessTokenDTO;
import com.ahss.uam.dm.dto.CreatUserEntityDTO;
import com.ahss.uam.dm.dto.EntityDTO;
import com.ahss.uam.dm.dto.ProductRedirectUrlDTO;
import com.ahss.uam.dm.dto.StateTokenDTO;
import com.ahss.uam.dm.dto.UserDetailsDTO;
import com.ahss.uam.dm.enums.SortDirection;
import com.ahss.uam.dm.enums.UserSortBy;
import com.ahss.uam.dm.mapper.UserEntityMapper;
import io.micrometer.observation.annotation.Observed;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/v1/api/uam/user")
@Tag(name = "User", description = "User Management")
@Observed
public class UamUserController {

  private final UamUserService uamUserService;
  private final UserEntityMapper userEntityMapper;

  public UamUserController(UamUserService uamUserService, UserEntityMapper userEntityMapper) {
    this.uamUserService = uamUserService;
    this.userEntityMapper = userEntityMapper;
  }


  @GetMapping()
  @Operation(summary = "Get Users", description = "Get all users")
  @Hidden
  public ResponseEntity<ApiResponse<List<EntityDTO>, Object>> getAllUser(
      @RequestParam(defaultValue = PageSupport.FIRST_PAGE_NUM) int page,
      @RequestParam(defaultValue = PageSupport.DEFAULT_PAGE_SIZE) int size) {
    List<UserEntity> result = uamUserService.getAllUsers(page, size);
    return ResponseEntity.ok(
        ApiResponse.<List<EntityDTO>, Object>builder()
            .status(ResponseStatus.SUCCESS)
            .responseCode(RC_200_000)
            .data(userEntityMapper.toDtos(result))
            .build());
  }

  @GetMapping("/search")
  @Operation(summary = "Search Users", description = "Search for users")
  public ResponseEntity<ApiResponse<List<EntityDTO>, Object>> searchUser(
      @RequestParam(required = false) String searchKey,
      @RequestParam(required = false) Integer tenantId,
      @RequestParam(defaultValue = PageSupport.FIRST_PAGE_NUM) int page,
      @RequestParam(defaultValue = PageSupport.DEFAULT_PAGE_SIZE) int size,
      @RequestParam(defaultValue = "ID") UserSortBy sortBy,
      @RequestParam(defaultValue = "ASC") SortDirection sortDirection) {
    PageSupport<UserEntity> result = uamUserService.searchUsers(searchKey, tenantId, page, size, sortBy.getValue(), sortDirection.name());
    return ResponseEntity.ok(
        ApiResponse.<List<EntityDTO>, Object>builder()
            .status(ResponseStatus.SUCCESS)
            .responseCode(RC_200_000)
            .data(userEntityMapper.toDtos(result.content()))
            .actions(result.pageInfo())
            .build());
  }


  @PostMapping(value = "")
  @Operation(summary = "Create an user", description = "Create User")
  @Hidden
  public ResponseEntity<ApiResponse<EntityDTO, Object>> createTenant(@Valid @RequestBody CreatUserEntityDTO entityDTO) {
    EntityDTO entity = userEntityMapper.toDto(uamUserService.createUser(entityDTO));
    return ResponseEntity.ok(ApiResponse.<EntityDTO, Object>builder()
        .status(ResponseStatus.SUCCESS)
        .responseCode(RC_200_000)
        .data(entity)
        .build());
  }

  @PutMapping(value = "")
  @Operation(summary = "Update an user", description = "Update User")
  @Hidden
  public ResponseEntity<ApiResponse<EntityDTO, Object>> updateTenant(@Valid @RequestBody EntityDTO entityDTO) {
    EntityDTO entity = userEntityMapper.toDto(uamUserService.updateUser(entityDTO));
    return ResponseEntity.ok(ApiResponse.<EntityDTO, Object>builder()
        .status(ResponseStatus.SUCCESS)
        .responseCode(RC_200_000)
        .data(entity)
        .build());
  }

  @DeleteMapping(value = "/{id}")
  @Operation(summary = "Delete an user", description = "Delete User")
  @Hidden
  public ResponseEntity<ApiResponse<Long, Object>> deleteUser(@Valid @PathVariable Long id) {
    uamUserService.deleteUser(id);
      return ResponseEntity.ok(ApiResponse
                                   .<Long, Object>builder()
        .status(ResponseStatus.SUCCESS)
        .responseCode(RC_200_000)
        .data(id)
        .build());
  }

  @GetMapping(value = "/{id}")
  @Operation(summary = "Get an user", description = "Get User's information")
  public ResponseEntity<ApiResponse<UserDetailsDTO, Object>> getUser(@Valid @PathVariable Long id) {
    UserEntity entity = uamUserService.getUser(id);
    return ResponseEntity.ok(ApiResponse.<UserDetailsDTO, Object>builder()
        .status(ResponseStatus.SUCCESS)
        .responseCode(RC_200_000)
        .data(userEntityMapper.toDetailDto(entity))
        .build());
  }

    @PostMapping(value = "/validate-user")
    @Operation(summary = "Validate user with token", description = "Validate user with token and create if user not exist")
    public ResponseEntity<ApiResponse<EntityDTO, Object>> validateUser() {
        EntityDTO entity = userEntityMapper.toDto(uamUserService.createOrUpdateUserByTokenInfo());
        return ResponseEntity.ok(ApiResponse
                                     .<EntityDTO, Object>builder()
                                     .status(ResponseStatus.SUCCESS)
                                     .responseCode(RC_200_000)
                                     .data(entity)
                                     .build());
    }

    @PostMapping("/generate-state-token/{appId}")
    @Operation(summary = "Generate redirect url", description = "Generate redirect url for app id")
    public ResponseEntity<ApiResponse<String, Object>> generateRedirectUrlForAppId(@PathVariable("appId") String appId,
                                                                                   @RequestBody ProductRedirectUrlDTO productRedirectUrlDTO) {
        String shortLiveToken = uamUserService.getAppRedirectUrl(appId, productRedirectUrlDTO.getRedirectUrl());
        return ResponseEntity.ok(
            ApiResponse.<String, Object>builder()
                       .status(ResponseStatus.SUCCESS)
                       .responseCode(RC_200_000)
                       .data(shortLiveToken)
                       .build());
    }

  @PreAuthorize("hasAnyAuthority('ADMIN', 'SYSTEM_USER')")
  @PostMapping("/validate-state-token")
  @Operation(summary = "Get access token", description = "Get access token using short live token")
  public ResponseEntity<ApiResponse<AccessTokenDTO, Object>> validateStateToken(@RequestBody StateTokenDTO stateToken) {
    String accessToken = uamUserService.getAccessToken(stateToken);
    return ResponseEntity.ok(
        ApiResponse.<AccessTokenDTO, Object>builder()
            .status(ResponseStatus.SUCCESS)
            .responseCode(RC_200_000)
            .data(AccessTokenDTO.builder().accessToken(accessToken).build())
            .build());
  }

    @PostMapping("/revoke-access-token")
    public ResponseEntity<ApiResponse<String, Object>> revokeAccessToken() {
        uamUserService.revokeAccessToken();
        return ResponseEntity.ok(
            ApiResponse
                .<String, Object>builder()
                .status(ResponseStatus.SUCCESS)
                .responseCode(RC_200_000)
                .data("Revoke access token successfully")
                .build());
    }
}
