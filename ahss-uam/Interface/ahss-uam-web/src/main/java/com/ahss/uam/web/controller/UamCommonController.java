package com.ahss.uam.web.controller;

import static com.ahss.common.api.AHSSResponseCode.RC_200_000;

import com.ahss.common.api.ApiResponse;
import com.ahss.common.api.ResponseStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping(value = "/v1/api/uam")
@ConditionalOnProperty(name = "ahpp.swagger.show-hidden", havingValue = "true", matchIfMissing = true)
public class UamCommonController {

    @GetMapping(value = "/hello")
    public ResponseEntity<ApiResponse<String, Object>> hello() {
        return ResponseEntity.ok(
            ApiResponse.<String, Object>builder().status(ResponseStatus.SUCCESS).responseCode(RC_200_000).data("Hello World").build());
    }
}
