package com.ahss.uam.web.controller;

import static com.ahss.common.api.AHSSResponseCode.RC_200_000;

import com.ahss.common.api.ApiResponse;
import com.ahss.common.api.ResponseStatus;
import com.ahss.uam.core.service.UamOrganizationService;
import com.ahss.uam.db.model.Organization;
import com.ahss.uam.dm.dto.OrganizationDTO;
import com.ahss.uam.dm.mapper.OrganizationMapper;
import io.micrometer.observation.annotation.Observed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/v1/api/uam/organization")
@Validated
@Observed
@Tag(name = "Organization", description = "Organization Management")
@ConditionalOnProperty(name = "ahpp.swagger.show-hidden", havingValue = "true", matchIfMissing = true)
public class UamOrganizationController {

    private final UamOrganizationService uamOrganizationService;
    private final OrganizationMapper organizationMapper;

    public UamOrganizationController(UamOrganizationService uamOrganizationService, OrganizationMapper organizationMapper) {
        this.uamOrganizationService = uamOrganizationService;
        this.organizationMapper = organizationMapper;
    }

    @GetMapping()
    public ResponseEntity<ApiResponse<List<OrganizationDTO>, Object>> getAllOrginization() {
        var result = uamOrganizationService.getAllOrganization();
        var dto = result.stream().map(organizationMapper::toDto).toList();
        return ResponseEntity.ok(
            ApiResponse.<List<OrganizationDTO>, Object>builder().status(ResponseStatus.SUCCESS).responseCode(RC_200_000).data(dto).build());
    }

    @PutMapping(value = "")
    @Operation(summary = "Update an organization", description = "Create Organization")
    public ResponseEntity<ApiResponse<OrganizationDTO, Object>> updateOrganization(@Valid @RequestBody OrganizationDTO organizationDTO) {
        OrganizationDTO organization = organizationMapper.toDto(uamOrganizationService.updateOrganization(organizationDTO));
        return ResponseEntity.ok(ApiResponse.<OrganizationDTO, Object>builder()
                                            .status(ResponseStatus.SUCCESS)
                                            .responseCode(RC_200_000)
                                            .data(organization)
                                            .build());
    }

    @DeleteMapping(value = "/{id}")
    @Operation(summary = "Delete an organization", description = "Create Organization")
    public ResponseEntity<ApiResponse<Integer, Object>> deleteOrganization(@Valid @PathVariable Integer id) {
        uamOrganizationService.deleteOrganization(id);
        return ResponseEntity.ok(ApiResponse.<Integer, Object>builder()
                                            .status(ResponseStatus.SUCCESS)
                                            .responseCode(RC_200_000)
                                            .data(id)
                                            .build());
    }

    @GetMapping(value = "/{id}")
    @Operation(summary = "Get an organization", description = "Get Organization's information")
    public ResponseEntity<ApiResponse<OrganizationDTO, Object>> getOrganization(@Valid @PathVariable Integer id) {
        Organization organization = uamOrganizationService.getOrganization(id);
        return ResponseEntity.ok(ApiResponse.<OrganizationDTO, Object>builder()
                                            .status(ResponseStatus.SUCCESS)
                                            .responseCode(RC_200_000)
                                            .data(organizationMapper.toDto(organization))
                                            .build());
    }
}
