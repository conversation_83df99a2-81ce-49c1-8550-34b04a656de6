package com.ahss.uam;

import com.microsoft.applicationinsights.attach.ApplicationInsights;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;


@SpringBootApplication(scanBasePackages = {"com.ahss.uam.web", "com.ahss.uam.config", "com.ahss.uam.db", "com.ahss.uam.core",
    "com.ahss.uam.dm.mapper", "com.ahss.common.config", "com.ahss.common.utils", "com.ahss.uam.db.repository", "com.ahss.uam.common.client"})
@EntityScan(basePackages = "com.ahss.uam.db.model")  // Exclude JOOQ POJO package
public class AHUamSpringApplication {

    public static void main(String[] args) {
        ApplicationInsights.attach();
        SpringApplication.run(AHUamSpringApplication.class, args);
    }
}
