package com.ahss.uam.web.controller;

import static com.ahss.common.api.AHSSResponseCode.RC_200_000;

import com.ahss.common.api.ApiResponse;
import com.ahss.common.api.ResponseStatus;
import com.ahss.common.utils.masking.SensitiveData;
import com.ahss.uam.core.service.UamPublicService;
import com.ahss.uam.db.model.SsoProvider;
import com.ahss.uam.dm.dto.SsoProviderDTO;
import io.micrometer.observation.annotation.Observed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.Email;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/v1/api/uam/public")
@Tag(name = "Public", description = "Public Endpoints")
@Observed
public class UamPublicController {

    private final UamPublicService uamPublicService;

    public UamPublicController(UamPublicService uamPublicService) {
        this.uamPublicService = uamPublicService;
    }

    @GetMapping(value = "/sso-url")
    @Operation(summary = "Get SSO URL", description = "Get SSO URL for provided email")
    @Cacheable(value = "users", key = "#email.substring(#email.indexOf('@') + 1)")
    public ResponseEntity<ApiResponse<SsoProviderDTO, Object>> getSSOUrl(@RequestParam @Email @SensitiveData(maskLength = 4) String email) {
        SsoProvider provider = uamPublicService.getSSOUrl(email);
        SsoProviderDTO ssoProviderDTO = SsoProviderDTO
            .builder()
            .ssoUrl(provider.getDiscoveryUrl())
            .clientId(provider.getClientId())
            .build();
        return ResponseEntity.ok(
            ApiResponse
                .<SsoProviderDTO, Object>builder()
                .status(ResponseStatus.SUCCESS)
                .responseCode(RC_200_000)
                .data(ssoProviderDTO)
                       .build());
    }

}
