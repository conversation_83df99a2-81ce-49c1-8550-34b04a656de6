<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.ahss</groupId>
        <artifactId>shared-services</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <groupId>com.ahss.uam</groupId>
    <artifactId>ahss-uam</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>Components/ahss-uam-common</module>
        <module>Components/ahss-uam-config</module>
        <module>Components/ahss-uam-core</module>
        <module>Components/ahss-uam-db</module>
        <module>Components/ahss-uam-domain</module>
        <module>Interface/ahss-uam-web</module>
    </modules>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

</project>