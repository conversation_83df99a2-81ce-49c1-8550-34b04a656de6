package com.ahss.uam.core.client;

import com.ahss.common.api.ApiResponse;
import com.ahss.uam.dm.dto.product.ProductDTO;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@RequiredArgsConstructor
@Component
public class ProductPortalClient {

    @Value("${product-portal.url}")
    private String productPortalUrl;

    private final RestTemplate restTemplate;

    public ProductDTO getProductFromPPService(String appId, List<String> tenantCodes, String token) {
        String url = productPortalUrl + "products/{appId}?tenantCodes={tenantCodes}";

        String tenantCodesParam = String.join(",", tenantCodes);

        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + token);

        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<ApiResponse<ProductDTO, Object>> response = restTemplate.exchange(
            url,
            HttpMethod.GET,
            entity,
            new ParameterizedTypeReference<ApiResponse<ProductDTO, Object>>() {
            },
            appId,
            tenantCodesParam
                                                                                        );

        if (response.getStatusCode().is2xxSuccessful()) {
            return response.getBody().getData();
        } else {
            if (response.getBody() == null) {
                throw new RuntimeException("Failed to get product from Product Portal service - " + response.getStatusCode());
            } else {
                throw new RuntimeException(
                    "Failed to get product from Product Portal service - " + response.getStatusCode() + " - " + response.getBody().getResponseCode()
                                                                                                                        .getDetailMessage());
            }
        }

    }
}
