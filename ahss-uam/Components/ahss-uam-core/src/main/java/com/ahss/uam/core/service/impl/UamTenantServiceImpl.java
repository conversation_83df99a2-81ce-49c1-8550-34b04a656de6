package com.ahss.uam.core.service.impl;

import com.ahss.common.api.AHSSResponseCode;
import com.ahss.common.domain.exception.BadRequestException;
import com.ahss.common.domain.exception.ResourceNotFoundException;
import com.ahss.uam.core.service.UamTenantService;
import com.ahss.uam.db.model.Tenant;
import com.ahss.uam.db.model.enums.TenantType;
import com.ahss.uam.db.repository.OrganizationRepository;
import com.ahss.uam.db.repository.TenantRepository;
import com.ahss.uam.dm.dto.CreateTenantDTO;
import com.ahss.uam.dm.dto.TenantDTO;
import com.ahss.uam.dm.mapper.TenantMapper;
import java.util.List;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Data
@Slf4j
public class UamTenantServiceImpl implements UamTenantService {

    private final TenantRepository tenantRepository;
    private final OrganizationRepository organizationRepository;
    private final TenantMapper tenantMapper;

    @Override
    public List<Tenant> getAllTenants() {
        return tenantRepository.findAll();
    }

    @Override
    public Tenant createTenant(CreateTenantDTO tenantDTO) {
        if (tenantDTO.getType() != TenantType.INDIVIDUAL && tenantDTO.getOrganization() == null) {
            throw new BadRequestException(AHSSResponseCode.RC_400_007);
        }
        if (tenantDTO.getParentTenantId() != null) {
            tenantRepository.findByTenantId(tenantDTO.getParentTenantId()).orElseThrow(ResourceNotFoundException::new);
        }
        Long count = tenantRepository.getCountTenantByCodeIncludingSuspended(tenantDTO.getTenantCode());
        if (count > 0) {
            throw new BadRequestException(AHSSResponseCode.RC_400_005);
        }
        Tenant tenant = tenantMapper.toEntityFromCreateDTO(tenantDTO);
        return tenantRepository.save(tenant);
    }

    @Override
    public Tenant updateTenant(TenantDTO tenantDTO) {
        Tenant tenant = tenantRepository.findByTenantId(tenantDTO.getTenantId()).orElseThrow(ResourceNotFoundException::new);
        Long count = tenantRepository.getCountTenantByCodeAndNotIdIncludingSuspended(tenantDTO.getTenantCode(), tenant.getTenantId());
        if (count > 0) {
            throw new BadRequestException(AHSSResponseCode.RC_400_005);
        }
        tenantMapper.partialUpdate(tenantDTO, tenant);
        return tenantRepository.save(tenant);
    }

    @Override
    public void deleteTenant(Integer id) {
        Tenant tenant = tenantRepository.findByTenantId(id).orElseThrow(ResourceNotFoundException::new);
        tenantRepository.delete(tenant);

    }

    @Override
    public Tenant getTenant(Integer id) {
        return tenantRepository.findByTenantId(id).orElseThrow(ResourceNotFoundException::new);
    }
}
