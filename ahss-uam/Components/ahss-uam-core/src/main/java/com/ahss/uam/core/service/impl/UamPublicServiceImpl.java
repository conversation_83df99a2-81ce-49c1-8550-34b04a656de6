package com.ahss.uam.core.service.impl;

import static com.ahss.common.api.AHSSResponseCode.RC_404_004;

import com.ahss.common.domain.exception.ResourceNotFoundException;
import com.ahss.uam.core.service.UamPublicService;
import com.ahss.uam.db.model.SsoProvider;
import com.ahss.uam.db.model.enums.TenantStatus;
import com.ahss.uam.db.repository.SsoProviderRepository;
import io.micrometer.observation.annotation.Observed;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Data
@Slf4j
@Observed
public class UamPublicServiceImpl implements UamPublicService {

    private final SsoProviderRepository ssoProviderRepository;

    @Override
    public SsoProvider getSSOUrl(String email) {
        String domain = email.substring(email.indexOf("@") + 1);
        SsoProvider provider = ssoProviderRepository.findByClientDomainAndTenant_TenantStatus(domain, TenantStatus.ACTIVE)
                                                    .stream()
                                                    .findFirst()
                                                    .orElseThrow(() -> new ResourceNotFoundException(RC_404_004));

        return provider;
    }
}
