package com.ahss.uam.core.client;

import java.util.Base64;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

@Slf4j
@RequiredArgsConstructor
@Component
public class OktaAuthorizationServerClient {

    private static final String REVOKE_ENDPOINT = "/v1/revoke";
    private final RestTemplate restTemplate;

    public void revokeAccessToken(String accessToken, String clientId, String clientSecret, String oktaIssuerUrl) {
        try {
            String credentials = clientId + ":" + clientSecret;
            String encodedCredentials = Base64
                .getEncoder()
                .encodeToString(credentials.getBytes());

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            headers.set("Authorization", "Basic " + encodedCredentials);

            MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
            body.add("token", accessToken);
            body.add("token_type_hint", "access_token");

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(body, headers);

            String url = oktaIssuerUrl + REVOKE_ENDPOINT;
            log.info("Revoke access token request URL: {}, headers: {}, body: {}", url, request.getHeaders(), body);

            restTemplate.postForEntity(url, request, String.class);
        } catch (Exception e) {
            throw new RuntimeException("Failed to revoke access token: " + e.getMessage(), e);
        }
    }
}
