package com.ahss.uam.core.service.hmac;

import com.ahss.uam.dm.dto.StateTokenDTO;
import com.ahss.uam.dm.request.StateVerificationRequest;

import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

public interface HMACService {

    StateTokenDTO generateHMAC(String data) throws NoSuchAlgorithmException, InvalidKeyException;

    /**
     * Verifies HMAC with timestamp validation
     */
    boolean verifyHMAC(StateVerificationRequest request) throws NoSuchAlgorithmException, InvalidKeyException;
}
