package com.ahss.uam.core.service.impl;

import com.ahss.common.api.AHSSResponseCode;
import com.ahss.common.api.PageSupport;
import com.ahss.common.domain.context.UserContext;
import com.ahss.common.domain.context.UserContextHolder;
import com.ahss.common.domain.exception.AccessDeniedException;
import com.ahss.common.domain.exception.BadRequestException;
import com.ahss.common.domain.exception.ResourceNotFoundException;
import com.ahss.uam.core.client.OktaAuthorizationServerClient;
import com.ahss.uam.core.client.ProductPortalClient;
import com.ahss.uam.core.service.AESEncryptionService;
import com.ahss.uam.core.service.UamUserService;
import com.ahss.uam.core.service.hmac.HMACService;
import com.ahss.uam.core.service.hmac.HMACServiceFactory;
import com.ahss.uam.db.model.EntityMapping;
import com.ahss.uam.db.model.SsoProvider;
import com.ahss.uam.db.model.StateCache;
import com.ahss.uam.db.model.Tenant;
import com.ahss.uam.db.model.TenantConfigStatus;
import com.ahss.uam.db.model.TenantEntity;
import com.ahss.uam.db.model.UserEntity;
import com.ahss.uam.db.model.enums.TenantStatus;
import com.ahss.uam.db.model.enums.UserNameType;
import com.ahss.uam.db.model.enums.UserStatus;
import com.ahss.uam.db.repository.EntityMappingRepository;
import com.ahss.uam.db.repository.SsoProviderRepository;
import com.ahss.uam.db.repository.StateCacheRepository;
import com.ahss.uam.db.repository.TenantConfigRepository;
import com.ahss.uam.db.repository.TenantRepository;
import com.ahss.uam.db.repository.UserEntityRepository;
import com.ahss.uam.dm.dto.EntityDTO;
import com.ahss.uam.dm.dto.StateTokenDTO;
import com.ahss.uam.dm.dto.product.ProductConfigDTO;
import com.ahss.uam.dm.dto.product.ProductDTO;
import com.ahss.uam.dm.dto.product.enums.ProductConfigType;
import com.ahss.uam.dm.mapper.UserEntityMapper;
import io.micrometer.observation.annotation.Observed;
import java.net.URI;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;

@Service
@Data
@Slf4j
@Observed
public class UamUserServiceImpl implements UamUserService {

    private static final int SHORT_LIVED_TOKEN_DURATION = 5;
    private static final int SHORT_LIVED_TOKEN_MIN_DURATION = 1;

    private final StateCacheRepository stateCacheRepository;
    private final UserEntityRepository userEntityRepository;
    private final EntityMappingRepository entityMappingRepository;
    private final TenantRepository tenantRepository;
    private final TenantConfigRepository tenantConfigRepository;
    private final SsoProviderRepository ssoProviderRepository;
    private final UserEntityMapper userEntityMapper;
    private final ProductPortalClient productPortalClient;
    private final OktaAuthorizationServerClient oktaAuthorizationServerClient;
    private final AESEncryptionService aesEncryptionService;
    @Qualifier("HMACServiceFactoryImpl")
    private final HMACServiceFactory hmacServiceFactory;

    @Value("${okta.api-gateway.client-id}")
    private String oktaApiGatewayClientId;

    @Override
    public List<UserEntity> getAllUsers(Integer page, Integer size) {
        return userEntityRepository.findAll();
    }

    @Override
    public PageSupport<UserEntity> searchUsers(String search, Integer tenantId, Integer page, Integer size, String sortBy, String sortDirection) {
        List<UserEntity> entities = userEntityRepository.searchUsers(search, tenantId, page, size, sortBy, sortDirection);
        Long count = userEntityRepository.countUser(search, tenantId);
        return new PageSupport<>(entities, page, size, count);
    }

    @Override
    public void revokeAccessToken() {
        String accessToken = UserContextHolder
            .getUserContext()
            .getAccessToken();
        if (accessToken == null || accessToken.isEmpty()) {
            throw new IllegalStateException("Access token is missing");
        }

        String email = UserContextHolder
            .getUserContext()
            .getEmail();
        if (email == null || email.isEmpty()) {
            throw new IllegalStateException("Email is missing");
        }

        String domain = email.substring(email.indexOf("@") + 1);
        SsoProvider ssoProvider = ssoProviderRepository
            .findByClientDomainAndTenant_TenantStatus(domain, TenantStatus.ACTIVE)
            .stream()
            .findFirst()
            .orElseThrow(() -> new IllegalStateException("Unable to find SSO information"));

        String clientSecret;
        try {
            clientSecret = aesEncryptionService.decrypt(ssoProvider.getClientSecret());
        } catch (Exception e) {
            throw new IllegalStateException("Unable to decrypt clientSecret", e);
        }
        String oktaIssuerUrl = ssoProvider.getDiscoveryUrl();
        oktaAuthorizationServerClient.revokeAccessToken(accessToken, oktaApiGatewayClientId, clientSecret, oktaIssuerUrl);
    }

    public UserEntity createUser(EntityDTO userDTO) {
        tenantRepository
            .findByTenantId(userDTO.getTenantId())
            .orElseThrow(() -> new ResourceNotFoundException(AHSSResponseCode.RC_404_003));
        if (userDTO.getParentEntityId() != null) {
            userEntityRepository
                .findById(userDTO.getParentEntityId())
                .orElseThrow(() -> new ResourceNotFoundException(AHSSResponseCode.RC_404_001));
        }
        UserEntity userEntity = userEntityMapper.toEntity(userDTO);
        return userEntityRepository.save(userEntity);
    }

    public UserEntity updateUser(EntityDTO userDTO) {
        if (userDTO.getEntityId() == null) {
            throw new ResourceNotFoundException(AHSSResponseCode.RC_404_001);
        }
        if (userDTO.getTenantId() != null) {
            tenantRepository
                .findByTenantId(userDTO.getTenantId())
                .orElseThrow(() -> new ResourceNotFoundException(AHSSResponseCode.RC_404_003));
        }

        if (userDTO.getParentEntityId() != null) {
            userEntityRepository
                .findById(userDTO.getParentEntityId())
                .orElseThrow(() -> new ResourceNotFoundException(AHSSResponseCode.RC_404_001));
        }
        UserEntity userEntity = userEntityRepository
            .findById(userDTO.getEntityId())
            .orElseThrow(ResourceNotFoundException::new);
        userEntityMapper.partialUpdate(userDTO, userEntity);
        return userEntityRepository.save(userEntity);
    }

    public void deleteUser(Long id) {
        UserEntity userEntity = userEntityRepository
            .findById(id)
            .orElseThrow(() -> new ResourceNotFoundException(AHSSResponseCode.RC_404_001));
        userEntityRepository.delete(userEntity);
    }

    public UserEntity getUser(Long id) {
        return userEntityRepository
            .findById(id)
            .orElseThrow(() -> new ResourceNotFoundException(AHSSResponseCode.RC_404_001));
    }

    public UserEntity createOrUpdateUserByTokenInfo() {
        UserContext userContext = UserContextHolder.getUserContext();
        String email = userContext.getEmail();
        String name = userContext.getName();
        String sub = userContext.getSub();
        List<String> memberOf = userContext.getMemberOf();
        List<Tenant> tenants = tenantConfigRepository.findDistinctTenantIdByConfigKeyInAndTenantConfigStatus(memberOf, TenantConfigStatus.ACTIVE);
        if (tenants.isEmpty()) {
            throw new AccessDeniedException(AHSSResponseCode.RC_403_000);
        }

        String identity = (email != null) ? email : sub;
        String identityType = (email != null) ? UserNameType.EMAIL.name() : UserNameType.USERNAME.name();

        if (identity == null) {
            throw new AccessDeniedException(AHSSResponseCode.RC_403_001);
        }

        Optional<EntityMapping> entityMappings = entityMappingRepository.findDistinctFirstBySystemIdAndExternalIdentifier(identity, identityType);
        if (entityMappings.isPresent()) {
            return entityMappings
                .get()
                .getUserEntity();
        }

        UserEntity userEntity = UserEntity
            .builder()
            .name((name != null) ? name : sub)
            .status(UserStatus.ACTIVE)
            .build();

        EntityMapping entityMapping = EntityMapping
            .builder()
            .systemId(identity)
            .externalIdentifier(identityType)
            .name((name != null) ? name : sub)
            .userEntity(userEntity)
            .build();

        userEntity.setEntityMappings(List.of(entityMapping));

        Set<TenantEntity> tenantEntities = tenants
            .stream()
            .map(t -> TenantEntity
                .builder()
                .tenant(t)
                .userEntity(userEntity)
                .build())
            .collect(Collectors.toSet());

        userEntity.setTenantEntities(tenantEntities);

        return userEntityRepository.save(userEntity);
    }

    protected StateTokenDTO getStateToken(String clientId) {
        HMACService hmacService;
        try {
            hmacService = hmacServiceFactory.getHMACService(clientId);
        } catch (IllegalArgumentException e) {
            throw new ResourceNotFoundException(AHSSResponseCode.RC_404_006);
        }
        String accessToken = UserContextHolder.getUserContext().getAccessToken();
        Optional<StateCache> shortLivedToken = stateCacheRepository.findFirstByAccessTokenAndExpiresAtGreaterThanAndUsedOrderByExpiresAtDesc(
            accessToken,
            LocalDateTime
                .now()
                .plusMinutes(
                    SHORT_LIVED_TOKEN_MIN_DURATION), false);
        try {
            if (shortLivedToken.isPresent()) {

                return hmacService.generateHMAC(shortLivedToken
                    .get()
                    .getState());

            } else {
                StateCache token = new StateCache();
                token.setAccessToken(accessToken);
                token.setState(UUID
                    .randomUUID()
                    .toString());
                token.setUsed(false);
                token.setExpiresAt(LocalDateTime
                    .now()
                    .plusMinutes(SHORT_LIVED_TOKEN_DURATION));
                stateCacheRepository.save(token);
                return hmacService.generateHMAC(token.getState());
            }
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new RuntimeException(e);
        }
    }

    public String getAppRedirectUrl(String appId, String redirectUrl) {
        StateTokenDTO stateToken = getStateToken(appId);
        List<String> tenants = tenantConfigRepository
            .findDistinctTenantIdByConfigKeyInAndTenantConfigStatus(
                UserContextHolder
                    .getUserContext()
                    .getMemberOf(), TenantConfigStatus.ACTIVE)
            .stream()
            .map(Tenant::getTenantCode)
            .collect(Collectors.toList());
        ProductDTO productDTO = productPortalClient.getProductFromPPService(appId, tenants,
            UserContextHolder
                .getUserContext()
                .getAccessToken());
        String productUrl = productDTO
            .getProductConfigs()
            .stream()
            .filter(p -> p.getConfigType() == ProductConfigType.URL)
            .map(ProductConfigDTO::getConfigValue)
            .findFirst()
            .orElseThrow(() -> new ResourceNotFoundException(AHSSResponseCode.RC_404_006));
        var newRedirectUrl = productUrl;
        if (StringUtils.hasLength(redirectUrl)) {
            String rootDomain = getRootDomain(redirectUrl);
            String productDomain = getRootDomain(productUrl);
            if (!rootDomain.equals(productDomain)) {
                throw new BadRequestException(AHSSResponseCode.RC_400_009);
            } else {
                newRedirectUrl = redirectUrl;
            }
        }
        StringBuilder result = new StringBuilder(newRedirectUrl)
            .append(newRedirectUrl.contains("?") ? "&" : "?")
            .append("state=")
            .append(stateToken.getState())
            .append("&timestamp=")
            .append(stateToken.getTimestamp())
            .append("&signature=")
            .append(stateToken.getSignature())
            .append("&appId=")
            .append(appId);

        return result.toString();
    }

    private String getRootDomain(String url) {
        try {
            URI uri = new URI(url);
            String domain = uri.getHost();
            String[] parts = domain.split("\\.");
            if (parts.length > 2) {
                return parts[parts.length - 2] + "." + parts[parts.length - 1];
            }
            return domain;
        } catch (Exception e) {
            throw new BadRequestException(AHSSResponseCode.RC_400_009);
        }
    }

    public String getAccessToken(StateTokenDTO token) {
        StateCache stateCache = stateCacheRepository
            .findByStateAndUsedAndExpiresAtGreaterThan(token.getState(), false, LocalDateTime.now())
            .orElseThrow(() -> new ResourceNotFoundException(AHSSResponseCode.RC_404_005));
        stateCache.setUsed(true);
        stateCacheRepository.save(stateCache);
        return stateCache.getAccessToken();
    }

}
