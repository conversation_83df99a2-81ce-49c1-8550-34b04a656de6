package com.ahss.uam.core.service.impl;

import com.ahss.common.api.AHSSResponseCode;
import com.ahss.common.domain.exception.BadRequestException;
import com.ahss.common.domain.exception.ResourceNotFoundException;
import com.ahss.uam.core.service.AESEncryptionService;
import com.ahss.uam.core.service.UamAdminTokenService;
import com.ahss.uam.db.model.ClientConfig;
import com.ahss.uam.db.model.SsoProvider;
import com.ahss.uam.db.model.Tenant;
import com.ahss.uam.db.model.enums.ClientConfigStatus;
import com.ahss.uam.db.repository.ClientConfigRepository;
import com.ahss.uam.db.repository.SsoProviderRepository;
import com.ahss.uam.db.repository.TenantRepository;
import com.ahss.uam.dm.dto.ClientConfigDTO;
import com.ahss.uam.dm.dto.CreateSsoProviderDTO;
import com.ahss.uam.dm.dto.ServerKeyDTO;
import com.ahss.uam.dm.mapper.SsoProviderMapper;
import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.JWSSigner;
import com.nimbusds.jose.crypto.MACSigner;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import java.util.Date;
import java.util.UUID;
import javax.crypto.spec.SecretKeySpec;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
@Data
@Slf4j
public class UamAdminTokenServiceImpl implements UamAdminTokenService {

    private final ClientConfigRepository clientConfigRepository;

    private final AESEncryptionService aesEncryptionService;

    private final SsoProviderRepository ssoProviderRepository;

    private final TenantRepository tenantRepository;

    private final SsoProviderMapper ssoProviderMapper;
    public ServerKeyDTO createClientConfig(ClientConfigDTO client) {
        clientConfigRepository.findByClientId(client.getClientId()).ifPresent(
            clientConfig -> {
                throw new BadRequestException(AHSSResponseCode.RC_400_010);
            });
        String xAPIKey = UUID.randomUUID().toString();
        String serverKey = UUID.randomUUID().toString();
        try {
            ClientConfig clientConfig = ClientConfig.builder().clientSharedKey(xAPIKey)
                                                    .clientId(client.getClientId())
                                                    .clientSecretKey(aesEncryptionService.encrypt(serverKey))
                                                    .clientAccessToken(generateToken(client.getClientId(), xAPIKey, serverKey))
                                                    .status(ClientConfigStatus.ACTIVE)
                                                    .build();
            clientConfigRepository.save(clientConfig);
            return ServerKeyDTO.builder().xAPIKey(xAPIKey).token(clientConfig.getClientAccessToken()).build();
        } catch (Exception e) {
            log.error("Error while creating client config", e);
            return null;
        }
    }

    public ServerKeyDTO updateClientConfig(ClientConfigDTO client) {
        ClientConfig clientConfigs = clientConfigRepository
            .findByClientIdAndClientSharedKey(client.getClientId(), client.getClientSharedKey())
            .orElseThrow(
                ResourceNotFoundException::new);
        try {
            clientConfigs.setClientAccessToken(generateToken(client.getClientId(), client.getClientSharedKey(),
                                                             aesEncryptionService.decrypt(clientConfigs.getClientSecretKey())));
            clientConfigRepository.save(clientConfigs);
            return ServerKeyDTO.builder().xAPIKey(clientConfigs.getClientSharedKey()).token(clientConfigs.getClientAccessToken()).build();
        } catch (Exception e) {
            throw new BadRequestException(AHSSResponseCode.RC_400_000);
        }
    }

    @Override
    public ServerKeyDTO getApiKeyByClientId(String clientId) {
        ClientConfig clientConfig = clientConfigRepository
            .findByClientId(clientId)
            .orElseThrow(
                ResourceNotFoundException::new);
        return ServerKeyDTO.builder().xAPIKey(clientConfig.getClientSharedKey()).token(clientConfig.getClientAccessToken()).build();
    }

    @Override
    public void deleteClientConfig(String clientId) {
        ClientConfig clientConfig = clientConfigRepository
            .findByClientId(clientId)
            .orElseThrow(
                ResourceNotFoundException::new);
        clientConfigRepository.delete(clientConfig);
    }

    @Override
    public String getSecretKeyByApiKey(String apiKey, String token) {
        try {
            String secretKey =
                clientConfigRepository.findByClientSharedKeyAndClientAccessTokenAndStatus(apiKey, token, ClientConfigStatus.ACTIVE).orElseThrow(
                    ResourceNotFoundException::new).getClientSecretKey();
            return aesEncryptionService.decrypt(secretKey);
        } catch (Exception e) {
            log.error("Error while getting secret key", e);
            return null;
        }
    }

    public String generateToken(String clientId, String apiKey, String serverKey) throws Exception {
        SecretKeySpec secretKey = new SecretKeySpec(serverKey.getBytes(), "HmacSHA256");
        JWSSigner signer = new MACSigner(secretKey);

        JWTClaimsSet claimsSet = new JWTClaimsSet.Builder()
            .issuer("AHPP")
            .subject(clientId)
            .claim("memberOf", "client")
            .jwtID(apiKey)
            .issueTime(new Date())
            .build();

        SignedJWT signedJWT = new SignedJWT(new JWSHeader(JWSAlgorithm.HS256), claimsSet);

        signedJWT.sign(signer);

        return signedJWT.serialize();
    }

    public SsoProvider createSsoUrl(CreateSsoProviderDTO createSsoProviderDTO) {
        Tenant tentant = tenantRepository
            .findByTenantCode(createSsoProviderDTO.getTenantCode())
            .orElseThrow(ResourceNotFoundException::new);
        SsoProvider ssoProvider = ssoProviderRepository
            .findByTenantCode(createSsoProviderDTO.getTenantCode())
            .orElse(SsoProvider
                        .builder()
                        .build());
        try {
            ssoProviderMapper.partialUpdate(createSsoProviderDTO, ssoProvider);
            ssoProvider.setTenant(tentant);
            ssoProvider.setClientId(createSsoProviderDTO.getClientId());
            if (StringUtils.hasLength(createSsoProviderDTO.getClientSecret())) {
                ssoProvider.setClientSecret(aesEncryptionService.encrypt(createSsoProviderDTO.getClientSecret()));
            }
            return ssoProviderRepository.save(ssoProvider);
        } catch (Exception e) {
            throw new BadRequestException(AHSSResponseCode.RC_400_011);
        }
    }

}
