package com.ahss.uam.core.service.hmac.impl;

import com.ahss.uam.core.service.hmac.HMACService;
import com.ahss.uam.dm.dto.StateTokenDTO;
import com.ahss.uam.dm.request.StateVerificationRequest;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Base64;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;

/**
 * This service should be initialized per client
 */
@Slf4j
public class HMACServiceImpl implements HMACService {

    private static final String HMAC_ALGORITHM = "HmacSHA256";
    private static final long MAX_TIMESTAMP_AGE_SECONDS = 300; // 5 minutes
    private static final long CLOCK_SKEW_SECONDS = 60; // 1 minute allowance for clock skew
    private final String sharedKey;

    public HMACServiceImpl(String sharedKey) {
        if (sharedKey == null || sharedKey.isEmpty()) {
            throw new IllegalArgumentException("Shared key cannot be null or empty");
        }
        this.sharedKey = sharedKey;
    }

    @Override
    public StateTokenDTO generateHMAC(String data) throws NoSuchAlgorithmException, InvalidKeyException {
        String timestamp = getCurrentUtcTimestamp();
        String messageToSign = createMessageToSign(data, timestamp);
        String hmac = calculateHMAC(messageToSign);

        return new StateTokenDTO(data, hmac, timestamp);

    }

    private String calculateHMAC(String messageToSign) throws NoSuchAlgorithmException, InvalidKeyException {
        SecretKeySpec secretKeySpec = new SecretKeySpec(sharedKey.getBytes(StandardCharsets.UTF_8), HMAC_ALGORITHM);

        Mac mac = Mac.getInstance(HMAC_ALGORITHM);
        mac.init(secretKeySpec);

        byte[] hmacBytes = mac.doFinal(messageToSign.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(hmacBytes);
    }

    @Override
    public boolean verifyHMAC(StateVerificationRequest request) throws NoSuchAlgorithmException, InvalidKeyException {
        // Validate timestamp
        if (!isTimestampValid(request.timestamp())) {
            return false;
        }

        // Verify HMAC
        String messageToVerify = createMessageToSign(request.data(), request.timestamp());
        String calculatedHMAC = calculateHMAC(messageToVerify);

        return constantTimeEquals(calculatedHMAC, request.signature());
    }

    private String createMessageToSign(String data, String timestamp) {
        return data + timestamp;
    }

    /**
     * Get current UTC timestamp in seconds
     */
    private String getCurrentUtcTimestamp() {
        return String.valueOf(Instant.now().getEpochSecond());
    }

    /**
     * Validates timestamp considering timezone and clock skew
     */
    private boolean isTimestampValid(String timestampStr) {
        try {
            long messageTimestamp = Long.parseLong(timestampStr);
            Instant messageTime = Instant.ofEpochSecond(messageTimestamp);
            Instant currentTime = Instant.now();

            // Check if timestamp is too old
            if (ChronoUnit.SECONDS.between(messageTime, currentTime) > MAX_TIMESTAMP_AGE_SECONDS) {
                return false;
            }

            // Check if timestamp is too far in the future (accounting for clock skew)
            return ChronoUnit.SECONDS.between(currentTime, messageTime) <= CLOCK_SKEW_SECONDS;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * Constant-time comparison of Base64-encoded HMACs This is the fixed version that properly handles Base64 encoding HMACs are typically Base64
     * encoded Need to compare actual bytes, not encoded strings
     */
    private boolean constantTimeEquals(String a, String b) {
        if (a == null || b == null) {
            return false;
        }

        try {
            // Decode Base64 strings to byte arrays
            byte[] aBytes = Base64.getDecoder().decode(a);
            byte[] bBytes = Base64.getDecoder().decode(b);

            // Check lengths before comparison
            if (aBytes.length != bBytes.length) {
                return false;
            }

            // Constant-time comparison of bytes
            int result = 0;
            for (int i = 0; i < aBytes.length; i++) {
                result |= aBytes[i] ^ bBytes[i];
            }
            return result == 0;
        } catch (IllegalArgumentException e) {
            // Handle invalid Base64 encoding
            return false;
        }
    }
}
