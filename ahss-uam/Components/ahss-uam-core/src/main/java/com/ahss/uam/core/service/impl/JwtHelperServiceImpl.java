package com.ahss.uam.core.service.impl;

import static com.ahss.common.utils.cryptography.SignerType.USER;

import com.ahss.common.utils.cryptography.KeyGeneratorUtil;
import com.ahss.common.utils.cryptography.SignerType;
import com.ahss.common.utils.cryptography.model.RsaKeyData;
import com.ahss.uam.core.service.JwtHelperService;
import com.ahss.uam.dm.builder.JwtTokenBuilder;
import com.ahss.uam.dm.builder.OktaTokenBuilder;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.JOSEObjectType;
import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.JWSSigner;
import com.nimbusds.jose.crypto.MACSigner;
import com.nimbusds.jose.crypto.RSASSASigner;
import com.nimbusds.jose.jwk.KeyUse;
import com.nimbusds.jose.jwk.RSAKey;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class JwtHelperServiceImpl implements JwtHelperService {

    private final RSAKey RSA_KEY;
    private final RSASSASigner USER_SIGNER;
    private final RsaKeyData KEY_DATA;

    private final String SECRET_KEY;
    private final JWSSigner SYSTEM_SIGNER;

    // Okta-specific constants
    private static final String OKTA_ISSUER = "https://aiasec.oktapreview.com/oauth2/ausgr4dyrr9dsFkpM1d7";
    private static final String OKTA_AUD = "api://default";
    private static final String OKTA_CID = "0oagr4dyqqDvF8R4P1d7";
    private static final String OKTA_TOKEN_TYPE_AT = "at+jwt";

    public JwtHelperServiceImpl(@Value("${ahpp.secret-key}") String secretKey) throws JOSEException {
        this.SECRET_KEY = secretKey;
        this.SYSTEM_SIGNER = new MACSigner(SECRET_KEY.getBytes());
        this.RSA_KEY = KeyGeneratorUtil.generateRsaJwk(UUID
                                                           .randomUUID()
                                                           .toString(), KeyUse.SIGNATURE);
        this.USER_SIGNER = new RSASSASigner(RSA_KEY);
        this.KEY_DATA = new RsaKeyData(RSA_KEY);
    }


    private JWSHeader createHeader(String type, String alg) {
        return new JWSHeader.Builder(null == alg ? JWSAlgorithm.RS256 : JWSAlgorithm.parse(alg))
            .keyID(RSA_KEY.getKeyID())
            .type(null == type ? JOSEObjectType.JWT : new JOSEObjectType(type)) // Add type header
            .build();
    }

    private JWSHeader createOktaHeader() {
        return createHeader(OKTA_TOKEN_TYPE_AT, JWSAlgorithm.RS256.getName());
    }

    @Override
    public String generateToken(JwtTokenBuilder builder, SignerType signerType) {
        try {
            SignedJWT signedJWT = new SignedJWT(
                createHeader(JOSEObjectType.JWT.getType(), builder.getAlgo()),
                createOriginalClaimsSet(builder)
            );
            signedJWT.sign(USER == signerType ? USER_SIGNER : SYSTEM_SIGNER);
            return signedJWT.serialize();
        } catch (JOSEException e) {
            log.error("Failed to generate JWT token", e);
            throw new RuntimeException("Could not generate JWT token", e);
        }
    }

    private JWTClaimsSet createOriginalClaimsSet(JwtTokenBuilder builder) {
        Instant now = Instant.now();

        var jwtClaimSetBuilder = new JWTClaimsSet.Builder()
            .jwtID(UUID
                       .randomUUID()
                       .toString())
            .issuer(builder.getIssuer())
            .subject(builder.getSubject())
            .audience(builder.getAudience())
            .issueTime(Date.from(now))
            .notBeforeTime(Date.from(now))
            .expirationTime(Date.from(now.plusSeconds(builder.getExpirationSeconds())))
            .claim("email", builder.getEmail())
            .claim("name", builder.getName())
            .claim("memberOf", builder.getMemberOf())
            .claim("scope", builder.getScopes());

        builder
            .getAdditionalClaims()
            .forEach(jwtClaimSetBuilder::claim);
        return jwtClaimSetBuilder.build();
    }

    @Override
    public String generateOktaToken(OktaTokenBuilder builder) {
        try {
            SignedJWT signedJWT = new SignedJWT(
                createOktaHeader(),
                createOktaClaimsSet(builder)
            );
            signedJWT.sign(USER_SIGNER);
            return signedJWT.serialize();
        } catch (JOSEException e) {
            log.error("Failed to generate Okta JWT token", e);
            throw new RuntimeException("Could not generate Okta JWT token", e);
        }
    }

    private JWTClaimsSet createOktaClaimsSet(OktaTokenBuilder builder) {
        Instant now = Instant.now();
        String jti = UUID
            .randomUUID()
            .toString();

        var claimsBuilder = new JWTClaimsSet.Builder()
            .jwtID(jti)
            .issuer(OKTA_ISSUER)
            .subject(builder.getSub())
            .audience(List.of(OKTA_AUD))
            .issueTime(Date.from(now))
            .notBeforeTime(Date.from(now))
            .expirationTime(Date.from(now.plusSeconds(builder.getExpirationSeconds())))
            .claim("ver", 1)
            .claim("jti", jti)
            .claim("auth_time", now.getEpochSecond())
            .claim("scp", builder.getScopes())
            .claim("cid", OKTA_CID)
            .claim("uid", builder.getSub())
            .claim("email", builder.getEmail())
            .claim("name", builder.getName())
            .claim("given_name", builder.getGivenName())
            .claim("family_name", builder.getFamilyName())
            .claim("memberOf", builder.getMemberOf());

        builder
            .getAdditionalClaims()
            .forEach(claimsBuilder::claim);
        return claimsBuilder.build();
    }

    @Override
    public String getPublicJwk() {
        return KEY_DATA.getPublicJwkJson();
    }

    @Override
    public String getSecretKey() {
        return SECRET_KEY;
    }

    @Override
    public RSAKey getRsaKey() {
        return RSA_KEY;
    }

    public String getPublicKeyPem() {
        return KEY_DATA.getPublicKeyPem();
    }

    public String getPrivateKeyPem() {
        return KEY_DATA.getPrivateKeyPem();
    }

    public String getKeyId() {
        return RSA_KEY.getKeyID();
    }
}
