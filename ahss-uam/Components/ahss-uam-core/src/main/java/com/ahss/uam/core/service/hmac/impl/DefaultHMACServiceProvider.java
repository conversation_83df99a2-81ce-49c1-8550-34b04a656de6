package com.ahss.uam.core.service.hmac.impl;

import com.ahss.uam.core.service.hmac.HMACService;
import com.ahss.uam.core.service.hmac.HMACServiceProvider;
import org.springframework.stereotype.Component;

@Component
public class DefaultHMACServiceProvider implements HMACServiceProvider {

    @Override
    public HMACService createHMACService(String sharedKey, String clientId) {
        return new HMACServiceImpl(sharedKey);
    }
}
