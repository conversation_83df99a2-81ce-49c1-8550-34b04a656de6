package com.ahss.uam.core.service.impl;

import com.ahss.uam.core.service.AESEncryptionService;
import java.nio.ByteBuffer;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.KeySpec;
import java.util.Base64;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AESEncryptionServiceImpl implements AESEncryptionService {

    private static final String ENCRYPT_ALGO = "AES/GCM/NoPadding";
    private static final int TAG_LENGTH_BIT = 128;
    private static final int IV_LENGTH_BYTE = 12;
    private static final int SALT_LENGTH_BYTE = 16;
    private static final int ITERATIONS = 65536;
    private static final String KEY_ALGORITHM = "AES";
    private static final String KEY_FACTORY_ALGORITHM = "PBKDF2WithHmacSHA256";

    private final String secretKey;

    public AESEncryptionServiceImpl(@Value("${ahpp.encryption.secret-key}") String secretKey) {
        if (secretKey == null || secretKey.isEmpty()) {
            throw new IllegalArgumentException("secretKey cannot be null or empty");
        }
        this.secretKey = secretKey;
    }

    @Override
    public String encrypt(String plaintext)
    throws NoSuchAlgorithmException, InvalidKeySpecException, NoSuchPaddingException, InvalidAlgorithmParameterException, InvalidKeyException,
        IllegalBlockSizeException, BadPaddingException {
        // Generate salt and IV
        byte[] salt = generateRandomNonce(SALT_LENGTH_BYTE);
        byte[] iv = generateRandomNonce(IV_LENGTH_BYTE);

        // Derive key from password
        SecretKey aesKeyFromPassword = deriveKey(secretKey.toCharArray(), salt);

        Cipher cipher = Cipher.getInstance(ENCRYPT_ALGO);
        cipher.init(Cipher.ENCRYPT_MODE, aesKeyFromPassword, new GCMParameterSpec(TAG_LENGTH_BIT, iv));

        byte[] cipherText = cipher.doFinal(plaintext.getBytes());

        // Combine salt, iv, and cipherText
        byte[] cipherTextWithIvSalt = ByteBuffer.allocate(salt.length + iv.length + cipherText.length).put(salt).put(iv).put(cipherText).array();

        return Base64.getEncoder().encodeToString(cipherTextWithIvSalt);
    }

    @Override
    public String decrypt(String ciphertext)
    throws NoSuchAlgorithmException, InvalidKeySpecException, NoSuchPaddingException, IllegalBlockSizeException, BadPaddingException,
        InvalidAlgorithmParameterException, InvalidKeyException {
        byte[] decoder = Base64.getDecoder().decode(ciphertext);

        // Extract salt, iv, and cipherText
        ByteBuffer bb = ByteBuffer.wrap(decoder);
        byte[] salt = new byte[SALT_LENGTH_BYTE];
        byte[] iv = new byte[IV_LENGTH_BYTE];
        bb.get(salt);
        bb.get(iv);
        byte[] cipherText = new byte[bb.remaining()];
        bb.get(cipherText);

        // Derive key from password
        SecretKey aesKeyFromPassword = deriveKey(secretKey.toCharArray(), salt);

        Cipher cipher = Cipher.getInstance(ENCRYPT_ALGO);
        cipher.init(Cipher.DECRYPT_MODE, aesKeyFromPassword, new GCMParameterSpec(TAG_LENGTH_BIT, iv));

        byte[] plainText = cipher.doFinal(cipherText);
        return new String(plainText);
    }

    private static byte[] generateRandomNonce(int length) {
        byte[] nonce = new byte[length];
        new SecureRandom().nextBytes(nonce);
        return nonce;
    }

    private static SecretKey deriveKey(char[] password, byte[] salt) throws NoSuchAlgorithmException, InvalidKeySpecException {
        SecretKeyFactory factory = SecretKeyFactory.getInstance(KEY_FACTORY_ALGORITHM);
        KeySpec spec = new PBEKeySpec(password, salt, ITERATIONS, 256);
        SecretKey tmp = factory.generateSecret(spec);
        return new SecretKeySpec(tmp.getEncoded(), KEY_ALGORITHM);
    }
}
