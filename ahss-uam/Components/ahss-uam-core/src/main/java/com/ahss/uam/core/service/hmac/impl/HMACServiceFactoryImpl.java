package com.ahss.uam.core.service.hmac.impl;

import com.ahss.uam.core.service.hmac.HMACService;
import com.ahss.uam.core.service.hmac.HMACServiceFactory;
import com.ahss.uam.core.service.hmac.HMACServiceProvider;
import com.ahss.uam.db.model.ClientConfig;
import com.ahss.uam.db.repository.ClientConfigRepository;
import lombok.Data;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import java.util.Optional;

@Data
@Component
public class HMACServiceFactoryImpl implements HMACServiceFactory {

    private final ClientConfigRepository clientConfigRepository;

    @Qualifier("defaultHMACServiceProvider")
    private final HMACServiceProvider hmacServiceProvider;

    @Cacheable(value = "hmacServiceCache", key = "#clientId")
    @Override
    public HMACService getHMACService(String clientId) throws IllegalArgumentException {
        Optional<ClientConfig> clientConfig = clientConfigRepository.findByClientId(clientId);
        String sharedKey =
            clientConfig
                .map(ClientConfig::getClientSharedKey)
                .orElseThrow(() -> new IllegalArgumentException("Invalid clientId: " + clientId));
        return hmacServiceProvider.createHMACService(sharedKey, clientId);
    }
}
