DO
$$
    DECLARE
        v_organization_id   BIGINT;
        v_organization_name TEXT   := 'AIA GCS';
        v_tenant_id         BIGINT;
        v_tenant_code       TEXT   := 'AIA_GCS';
        v_tenant_name       TEXT   := 'AIA GCS';
        v_tenant_path       TEXT   := '/AIA/AIA_GCS';
        v_tenant_type       TEXT   := 'BUSINESS_IN';
        v_tenant_status     TEXT   := 'ACTIVE';
        v_product_code      TEXT   := 'AHIS';
        v_country           TEXT   := 'SG';
        v_region            TEXT   := 'APAC';
        v_company           TEXT   := 'AH';
        v_ad_group          TEXT[] := ARRAY ['AH_AHIS_AIA_GCS_USER_PROD', 'AH_AHIS_AIA_GCS_ADMIN_PROD'];
        v_role_list         TEXT[] := ARRAY ['USER', 'ADMIN'];
        i                   INT;
    BEGIN
        -- Sync sequences of tables before inserting.
        IF
            (SELECT max(org_id)
             FROM UAM.organization) IS NOT NULL THEN
            PERFORM setval('UAM.organization_org_id_seq',
                           greatest(
                               (SELECT max(org_id) FROM UAM.organization),
                               (SELECT last_value FROM UAM.organization_org_id_seq)
                           ));
        END IF;

        IF
            (SELECT MAX(tenant_id)
             FROM UAM.tenant) IS NOT NULL THEN
            PERFORM setval('UAM.tenant_tenant_id_seq',
                           GREATEST(
                               (SELECT MAX(tenant_id) FROM UAM.tenant),
                               (SELECT last_value FROM UAM.tenant_tenant_id_seq)
                           ));
        END IF;

        IF
            (SELECT MAX(tenant_config_id)
             FROM UAM.tenant_config) IS NOT NULL THEN
            PERFORM setval('UAM.tenant_config_tenant_config_id_seq',
                           GREATEST(
                               (SELECT MAX(tenant_config_id) FROM UAM.tenant_config),
                               (SELECT last_value FROM UAM.tenant_config_tenant_config_id_seq)
                           ));
        END IF;

        -- Check and insert organization.
        IF
            EXISTS (SELECT 1 FROM UAM.organization WHERE name = v_organization_name) THEN
            IF (SELECT country FROM UAM.organization WHERE name = v_organization_name) IS DISTINCT FROM v_country THEN
                UPDATE UAM.organization
                SET country    = v_country,
                    updated_at = CURRENT_TIMESTAMP,
                    updated_by = 'system'
                WHERE name = v_organization_name;
            END IF;
            SELECT org_id
            INTO v_organization_id
            FROM UAM.organization
            WHERE name = v_organization_name
            LIMIT 1;
        ELSE
            INSERT INTO UAM.organization (name, country, created_at, updated_at, created_by, updated_by)
            VALUES (v_organization_name,
                    v_country,
                    CURRENT_TIMESTAMP,
                    CURRENT_TIMESTAMP,
                    'system',
                    'system')
            RETURNING org_id INTO v_organization_id;
        END IF;

        -- Check and insert a new tenant using the retrieved org_id
        IF
            EXISTS(SELECT 1 FROM UAM.tenant WHERE tenant_code = v_tenant_code) THEN
            IF (
                    (SELECT organization_id FROM UAM.tenant WHERE tenant_code = v_tenant_code) IS DISTINCT FROM v_organization_id
                    OR (SELECT name FROM UAM.tenant WHERE tenant_code = v_tenant_code) IS DISTINCT FROM v_tenant_name
                    OR (SELECT path FROM UAM.tenant WHERE tenant_code = v_tenant_code) IS DISTINCT FROM v_tenant_path
                    OR (SELECT type FROM UAM.tenant WHERE tenant_code = v_tenant_code) IS DISTINCT FROM v_tenant_type::tenant_type
                    OR (SELECT tenant_status FROM UAM.tenant WHERE tenant_code = v_tenant_code) IS DISTINCT FROM v_tenant_status::tenant_status
                ) THEN
                UPDATE UAM.tenant
                SET organization_id = v_organization_id,
                    name            = v_tenant_name,
                    path            = v_tenant_path,
                    type            = v_tenant_type::tenant_type,
                    tenant_status   = v_tenant_status::tenant_status,
                    updated_at      = CURRENT_TIMESTAMP,
                    updated_by      = 'system'
                WHERE tenant_code = v_tenant_code;
            END IF;
            SELECT tenant_id
            INTO v_tenant_id
            FROM UAM.tenant
            WHERE tenant_code = v_tenant_code
            LIMIT 1;
        ELSE
            INSERT INTO UAM.tenant (organization_id, tenant_code, name, path, type, tenant_status, created_at, updated_at, created_by,
                                    updated_by)
            VALUES (v_organization_id,
                    v_tenant_code,
                    v_tenant_name,
                    v_tenant_path,
                    v_tenant_type::tenant_type,
                    v_tenant_status::tenant_status,
                    CURRENT_TIMESTAMP,
                    CURRENT_TIMESTAMP,
                    'system',
                    'system')
            RETURNING tenant_id INTO v_tenant_id;
        END IF;

        -- Use the retrieved tenant_id for tenant configurations
        FOR i IN 1..array_length(v_ad_group, 1)
            LOOP
                -- PRODUCT
                IF EXISTS (SELECT 1
                           FROM UAM.tenant_config
                           WHERE tenant_id = v_tenant_id
                             AND tenant_code = v_tenant_code
                             AND config_type = 'PRODUCT'
                             AND tenant_config_status = 'ACTIVE'
                             AND config_key = v_ad_group[i]) THEN
                    IF (SELECT config_value
                        FROM UAM.tenant_config
                        WHERE tenant_id = v_tenant_id
                          AND tenant_code = v_tenant_code
                          AND config_type = 'PRODUCT'
                          AND tenant_config_status = 'ACTIVE'
                          AND config_key = v_ad_group[i]) IS DISTINCT FROM v_product_code THEN
                        UPDATE UAM.tenant_config
                        SET config_value = v_product_code,
                            updated_at   = CURRENT_TIMESTAMP,
                            updated_by   = 'system'
                        WHERE tenant_id = v_tenant_id
                          AND tenant_code = v_tenant_code
                          AND config_type = 'PRODUCT'
                          AND tenant_config_status = 'ACTIVE'
                          AND config_key = v_ad_group[i];
                    END IF;
                ELSE
                    INSERT INTO UAM.tenant_config (tenant_id, tenant_code, config_type, config_value, config_value_format_type,
                                                   tenant_config_status, created_at, updated_at, created_by, updated_by, config_key)
                    VALUES (v_tenant_id,
                            v_tenant_code,
                            'PRODUCT',
                            v_product_code,
                            'FREE_TEXT',
                            'ACTIVE',
                            CURRENT_TIMESTAMP,
                            CURRENT_TIMESTAMP,
                            'system',
                            'system',
                            v_ad_group[i]);
                END IF;

                -- LOCATION_COUNTRY
                IF
                    EXISTS (SELECT 1
                            FROM UAM.tenant_config
                            WHERE tenant_id = v_tenant_id
                              AND tenant_code = v_tenant_code
                              AND config_type = 'LOCATION_COUNTRY'
                              AND tenant_config_status = 'ACTIVE'
                              AND config_key = v_ad_group[i]) THEN
                    IF (SELECT config_value
                        FROM UAM.tenant_config
                        WHERE tenant_id = v_tenant_id
                          AND tenant_code = v_tenant_code
                          AND config_type = 'LOCATION_COUNTRY'
                          AND tenant_config_status = 'ACTIVE'
                          AND config_key = v_ad_group[i]) IS DISTINCT FROM v_country THEN
                        UPDATE UAM.tenant_config
                        SET config_value = v_country,
                            updated_at   = CURRENT_TIMESTAMP,
                            updated_by   = 'system'
                        WHERE tenant_id = v_tenant_id
                          AND tenant_code = v_tenant_code
                          AND config_type = 'LOCATION_COUNTRY'
                          AND tenant_config_status = 'ACTIVE'
                          AND config_key = v_ad_group[i];
                    END IF;
                ELSE
                    INSERT INTO UAM.tenant_config (tenant_id, tenant_code, config_type, config_value, config_value_format_type,
                                                   tenant_config_status, created_at, updated_at, created_by, updated_by, config_key)
                    VALUES (v_tenant_id,
                            v_tenant_code,
                            'LOCATION_COUNTRY',
                            v_country,
                            'FREE_TEXT',
                            'ACTIVE',
                            CURRENT_TIMESTAMP,
                            CURRENT_TIMESTAMP,
                            'system',
                            'system',
                            v_ad_group[i]);
                END IF;

                -- LOCATION_REGION
                IF
                    EXISTS (SELECT 1
                            FROM UAM.tenant_config
                            WHERE tenant_id = v_tenant_id
                              AND tenant_code = v_tenant_code
                              AND config_type = 'LOCATION_REGION'
                              AND tenant_config_status = 'ACTIVE'
                              AND config_key = v_ad_group[i]) THEN
                    IF (SELECT config_value
                        FROM UAM.tenant_config
                        WHERE tenant_id = v_tenant_id
                          AND tenant_code = v_tenant_code
                          AND config_type = 'LOCATION_REGION'
                          AND tenant_config_status = 'ACTIVE'
                          AND config_key = v_ad_group[i]) IS DISTINCT FROM v_region THEN
                        UPDATE UAM.tenant_config
                        SET config_value = v_region,
                            updated_at   = CURRENT_TIMESTAMP,
                            updated_by   = 'system'
                        WHERE tenant_id = v_tenant_id
                          AND tenant_code = v_tenant_code
                          AND config_type = 'LOCATION_REGION'
                          AND tenant_config_status = 'ACTIVE'
                          AND config_key = v_ad_group[i];
                    END IF;
                ELSE
                    INSERT INTO UAM.tenant_config (tenant_id, tenant_code, config_type, config_value, config_value_format_type,
                                                   tenant_config_status, created_at, updated_at, created_by, updated_by, config_key)
                    VALUES (v_tenant_id,
                            v_tenant_code,
                            'LOCATION_REGION',
                            v_region,
                            'FREE_TEXT',
                            'ACTIVE',
                            CURRENT_TIMESTAMP,
                            CURRENT_TIMESTAMP,
                            'system',
                            'system',
                            v_ad_group[i]);
                END IF;

                -- COMPANY
                IF
                    EXISTS (SELECT 1
                            FROM UAM.tenant_config
                            WHERE tenant_id = v_tenant_id
                              AND tenant_code = v_tenant_code
                              AND config_type = 'COMPANY'
                              AND tenant_config_status = 'ACTIVE'
                              AND config_key = v_ad_group[i]) THEN
                    IF (SELECT config_value
                        FROM UAM.tenant_config
                        WHERE tenant_id = v_tenant_id
                          AND tenant_code = v_tenant_code
                          AND config_type = 'COMPANY'
                          AND tenant_config_status = 'ACTIVE'
                          AND config_key = v_ad_group[i]) IS DISTINCT FROM v_company THEN
                        UPDATE UAM.tenant_config
                        SET config_value = v_company,
                            updated_at   = CURRENT_TIMESTAMP,
                            updated_by   = 'system'
                        WHERE tenant_id = v_tenant_id
                          AND tenant_code = v_tenant_code
                          AND config_type = 'COMPANY'
                          AND tenant_config_status = 'ACTIVE'
                          AND config_key = v_ad_group[i];
                    END IF;
                ELSE
                    INSERT INTO UAM.tenant_config (tenant_id, tenant_code, config_type, config_value, config_value_format_type,
                                                   tenant_config_status, created_at, updated_at, created_by, updated_by, config_key)
                    VALUES (v_tenant_id,
                            v_tenant_code,
                            'COMPANY',
                            v_company,
                            'FREE_TEXT',
                            'ACTIVE',
                            CURRENT_TIMESTAMP,
                            CURRENT_TIMESTAMP,
                            'system',
                            'system',
                            v_ad_group[i]);
                END IF;

                -- ROLE
                IF
                    EXISTS (SELECT 1
                            FROM UAM.tenant_config
                            WHERE tenant_id = v_tenant_id
                              AND tenant_code = v_tenant_code
                              AND config_type = 'ROLE'
                              AND tenant_config_status = 'ACTIVE'
                              AND config_key = v_ad_group[i]) THEN
                    IF (SELECT config_value
                        FROM UAM.tenant_config
                        WHERE tenant_id = v_tenant_id
                          AND tenant_code = v_tenant_code
                          AND config_type = 'ROLE'
                          AND tenant_config_status = 'ACTIVE'
                          AND config_key = v_ad_group[i]) IS DISTINCT FROM v_role_list[i] THEN
                        UPDATE UAM.tenant_config
                        SET config_value = v_role_list[i],
                            updated_at   = CURRENT_TIMESTAMP,
                            updated_by   = 'system'
                        WHERE tenant_id = v_tenant_id
                          AND tenant_code = v_tenant_code
                          AND config_type = 'ROLE'
                          AND tenant_config_status = 'ACTIVE'
                          AND config_key = v_ad_group[i];
                    END IF;
                ELSE
                    INSERT INTO UAM.tenant_config (tenant_id, tenant_code, config_type, config_value, config_value_format_type,
                                                   tenant_config_status, created_at, updated_at, created_by, updated_by, config_key)
                    VALUES (v_tenant_id,
                            v_tenant_code,
                            'ROLE',
                            v_role_list[i],
                            'FREE_TEXT',
                            'ACTIVE',
                            CURRENT_TIMESTAMP,
                            CURRENT_TIMESTAMP,
                            'system',
                            'system',
                            v_ad_group[i]);
                END IF;
            END LOOP;
    END
$$;

