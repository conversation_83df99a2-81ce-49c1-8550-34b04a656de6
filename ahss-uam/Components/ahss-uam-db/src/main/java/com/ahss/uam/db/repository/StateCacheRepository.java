package com.ahss.uam.db.repository;

import com.ahss.uam.db.model.StateCache;
import io.micrometer.observation.annotation.Observed;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;

@Observed
@Repository
public interface StateCacheRepository extends JpaRepository<StateCache, String> {

  Optional<StateCache> findByStateAndUsedAndExpiresAtGreaterThan(String token, boolean used, LocalDateTime expiredAt);

  Optional<StateCache> findFirstByAccessTokenAndExpiresAtGreaterThanAndUsedOrderByExpiresAtDesc(String accessToken, LocalDateTime expiresAt, boolean used);

}
