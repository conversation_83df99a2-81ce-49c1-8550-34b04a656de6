package com.ahss.uam.config;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.converter.Converter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CustomJwtAuthenticationConverter implements Converter<Jwt, AbstractAuthenticationToken> {

    private static final String JWT_CLAIM_NAME_SCOPE = "scope";
    private static final String JWT_CLAIM_NAME_SCP = "scp";
    private static final String JWT_CLAIM_NAME_MEMBEROF = "memberOf";

    @Override
    public AbstractAuthenticationToken convert(Jwt jwt) {
        Collection<GrantedAuthority> authorities = extractAuthorities(jwt);
        log.debug("Extracted authorities: {}", authorities);
        return new JwtAuthenticationToken(jwt, authorities);
    }

    private Collection<GrantedAuthority> extractAuthorities(Jwt jwt) {
        Set<GrantedAuthority> authorities = new HashSet<>();

        // Add scope-based authorities
        List<String> scopes = jwt.getClaimAsStringList(JWT_CLAIM_NAME_SCOPE);
        if (scopes != null) {
            authorities.addAll(scopes
                                   .stream()
                                   .map(SimpleGrantedAuthority::new)
                                   .toList());
        }

        // Add memberOf-based authorities
        List<String> memberOf = jwt.getClaimAsStringList(JWT_CLAIM_NAME_MEMBEROF);
        if (memberOf != null) {
            authorities.addAll(memberOf
                                   .stream()
                                   .map(SimpleGrantedAuthority::new)
                                   .toList());
        }

        // Add token-based authorities
        List<String> tokenScopes = jwt.getClaimAsStringList(JWT_CLAIM_NAME_SCP);
        if (tokenScopes != null) {
            authorities.addAll(tokenScopes
                                   .stream()
                                   .map(SimpleGrantedAuthority::new)
                                   .toList());
        }

        return authorities;
    }
}
