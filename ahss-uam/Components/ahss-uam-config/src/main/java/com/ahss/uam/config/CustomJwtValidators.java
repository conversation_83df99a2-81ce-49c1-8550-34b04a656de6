package com.ahss.uam.config;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.function.Predicate;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.oauth2.core.OAuth2TokenValidator;
import org.springframework.security.oauth2.core.OAuth2TokenValidatorResult;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtClaimValidator;
import org.springframework.util.Assert;

public class CustomJwtValidators {


    private CustomJwtValidators() {}

    public static OAuth2TokenValidator<Jwt> createAudienceValidator(Collection<String> requiredAudiences) {
        return jwt -> {
            List<String> scopes = jwt.getClaimAsStringList("scp");
            if (scopes == null || !new HashSet<>(scopes).containsAll(requiredAudiences)) {
                OAuth2Error error = new OAuth2Error(
                    "invalid_audience",
                    "The token does not have the required scopes: " + requiredAudiences,
                    null
                );
                return OAuth2TokenValidatorResult.failure(error);
            }
            return OAuth2TokenValidatorResult.success();
        };
    }

    public static OAuth2TokenValidator<Jwt> createScopeValidator(Collection<String> requiredScopes) {
        return jwt -> {
            List<String> scopes = jwt.getClaimAsStringList("scp");
            if (scopes == null || !new HashSet<>(scopes).containsAll(requiredScopes)) {
                OAuth2Error error = new OAuth2Error(
                    "invalid_scope",
                    "The token does not have the required scopes: " + requiredScopes,
                    null
                );
                return OAuth2TokenValidatorResult.failure(error);
            }
            return OAuth2TokenValidatorResult.success();
        };
    }

    public static OAuth2TokenValidator<Jwt> createGroupValidator(Collection<String> requiredGroups) {
        Assert.notEmpty(requiredGroups, "Required groups must not be empty");

        return jwt -> {
            List<String> groups = jwt.getClaimAsStringList("memberOf");
            if (groups == null || !groups.containsAll(requiredGroups)) {
                OAuth2Error error = new OAuth2Error(
                    "invalid_groups",
                    "The token does not have the required groups: " + requiredGroups,
                    null
                );
                return OAuth2TokenValidatorResult.failure(error);
            }
            return OAuth2TokenValidatorResult.success();
        };
    }

    public static <T> OAuth2TokenValidator<Jwt> createClaimValidator(
        String claimName,
        Predicate<T> validation,
        String errorDescription) {
        return new JwtClaimValidator<>(claimName, validation) ;
    }
}
