package com.ahss.uam.config;

import com.ahss.uam.core.service.UamAdminTokenService;
import com.nimbusds.jwt.JWT;
import com.nimbusds.jwt.JWTParser;
import jakarta.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationManagerResolver;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.server.resource.InvalidBearerTokenException;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationProvider;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class CustomAuthenticationManagerResolver implements AuthenticationManagerResolver<HttpServletRequest> {

    private static final String API_KEY_HEADER = "X-API-KEY";
    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String AUTH_TYPE_SERVER = "server";
    private static final String AUTH_TYPE_SERVER_KEY_DELIMITER = "::";
    private static final String AUTH_TYPE_CLIENT_KEY_DELIMITER = "::";
    private static final String AUTH_TYPE_CLIENT = "client";

    private final JwtDecoderFactory jwtDecoderFactory;
    private final CustomJwtAuthenticationConverter jwtAuthenticationConverter;
    private final UamAdminTokenService uamAdminTokenService;

    @Value("${security.cache.manager.enabled:true}")
    private boolean cacheEnabled;

    private final Map<String, AuthenticationManager> authManagerCache = new ConcurrentHashMap<>();

    private List<IssuerConfig> issuers;

    @Autowired
    public void setIssuers(JwtIssuerProperties jwtIssuerProperties) {
        this.issuers = jwtIssuerProperties.getIssuers();
    }

    @Override
    public AuthenticationManager resolve(HttpServletRequest request) {
        try {
            String authType = determineAuthType(request);
            AuthenticationManager manager = cacheEnabled ?
                getCachedAuthenticationManager(authType, request) :
                createAuthenticationManager(authType, request);
            log.debug("Resolved authentication manager for type: {}", authType);
            return manager;
        } catch (Exception e) {
            log.error("Failed to resolve authentication manager: {}", e.getMessage());
            throw new InvalidBearerTokenException("Unable to resolve authentication manager", e);
        }
    }

    private String determineAuthType(HttpServletRequest request) {
        String apiKey = request.getHeader(API_KEY_HEADER);
        if (apiKey == null || apiKey.isEmpty()) {
            return AUTH_TYPE_CLIENT;
        }
        return AUTH_TYPE_SERVER;
    }

    private AuthenticationManager getCachedAuthenticationManager(String authType, HttpServletRequest request) {
        String key =
            AUTH_TYPE_SERVER.equals(authType) ? authType + AUTH_TYPE_SERVER_KEY_DELIMITER + request.getHeader(API_KEY_HEADER)
                : authType + AUTH_TYPE_CLIENT_KEY_DELIMITER + getIssuerFromToken(request);
        return authManagerCache.computeIfAbsent(key, cacheKey -> createAuthenticationManager(authType, request));
    }

    private AuthenticationManager createAuthenticationManager(String authType, HttpServletRequest request) {
        JwtDecoder decoder = getAppropriateDecoder(authType, request);
        JwtAuthenticationProvider provider = new JwtAuthenticationProvider(decoder);
        provider.setJwtAuthenticationConverter(jwtAuthenticationConverter);
        return new ProviderManager(Collections.singletonList(provider));
    }

    private JwtDecoder getAppropriateDecoder(String authType, HttpServletRequest request) {
        if (AUTH_TYPE_SERVER.equals(authType)) {
            String secret = uamAdminTokenService.getSecretKeyByApiKey(request.getHeader(API_KEY_HEADER),
                                                                      request
                                                                          .getHeader(AUTHORIZATION_HEADER)
                                                                          .substring(7));
            if (secret == null) {
                throw new InvalidBearerTokenException("Invalid API Key");
            }
            return jwtDecoderFactory.getServerJwtDecoder(secret);
        }

        String issuer = getIssuerFromToken(request);
        return issuers.stream()
            .filter(config -> config.getIssuer().equals(issuer))
            .findFirst()
            .map(config -> jwtDecoderFactory.getClientJwtDecoder(config.getIssuer(), config.getJwkSetUri(), config.getAudience()))
            .orElseThrow(() -> new InvalidBearerTokenException("Unknown issuer: " + issuer));
    }

    public void clearCache() {
        authManagerCache.clear();
        log.info("Authentication manager cache cleared");
    }

    /**
     * For testing only.
     * @return unmodifiable map of authManagerCache
     */
    public Map<String, AuthenticationManager> getAuthManagerCache() {
        return Collections.unmodifiableMap(authManagerCache);
    }

    private String getIssuerFromToken(HttpServletRequest request) {
        String token = request.getHeader(AUTHORIZATION_HEADER).substring(7);
        try {
            JWT jwt = JWTParser.parse(token);
            return jwt.getJWTClaimsSet().getIssuer();
        } catch (ParseException e) {
            throw new InvalidBearerTokenException("Invalid JWT token", e);
        }
    }

    @Data
    public static class IssuerConfig {
        private String issuer;
        private String jwkSetUri;
        private String audience;
    }
}
