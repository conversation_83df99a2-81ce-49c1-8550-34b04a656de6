package com.ahss.uam.config;

import com.ahss.uam.config.CustomAuthenticationManagerResolver.IssuerConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import java.util.List;

@Data
@Configuration
@ConfigurationProperties(prefix = "security.jwt")
public class JwtIssuerProperties {

    private List<IssuerConfig> issuers;
}
