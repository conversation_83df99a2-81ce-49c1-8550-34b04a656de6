package com.ahss.uam.config;

import com.ahss.uam.dm.enums.Authority;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.oauth2.server.resource.web.authentication.BearerTokenAuthenticationFilter;
import org.springframework.security.web.SecurityFilterChain;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity
@RequiredArgsConstructor
//@EnableConfigurationProperties(JwtIssuerProperties.class)
public class SecurityConfig {

    private final CustomJwtAuthenticationFilter customJwtAuthenticationFilter;

    private final CustomAuthenticationManagerResolver authenticationManagerResolver;

    private static final String[] AUTH_WHITELIST = {
        "/v3/api-docs",
        "/v3/api-docs/**",
        "/swagger-resources",
        "/swagger-resources/**",
        "/swagger-ui.html",
        "/swagger-ui/**",
        "/actuator/**",
        "/v1/api/uam/public/**",
        "*/v3/api-docs",
        "*/v3/api-docs/**",
        "*/swagger-resources",
        "*/swagger-resources/**",
        "*/swagger-ui.html",
        "*/swagger-ui/**",
        "*/actuator/**",
        "*/v1/api/uam/public/**"
    };

    private static final String[] SERVER_CLIENT_WHITELIST = {
        "v1/api/uam/user/validate-state-token",
    };

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
            .csrf(csrf -> csrf.disable())
            .authorizeHttpRequests(authorize -> authorize
                                       .requestMatchers(AUTH_WHITELIST)
                                       .permitAll()
                                       .requestMatchers(SERVER_CLIENT_WHITELIST)
                                       .hasAnyAuthority(Authority.SYSTEM_USER.getAuthority(),
                                                        Authority.ADMIN.getAuthority())
                                       .anyRequest()
                                       .hasAnyAuthority(Authority.USER.getAuthority(), Authority.ADMIN.getAuthority())
                                  )
            .oauth2ResourceServer(oauth2 -> oauth2
                                      .authenticationManagerResolver(authenticationManagerResolver)
                                 )
            .addFilterAfter(customJwtAuthenticationFilter, BearerTokenAuthenticationFilter.class);
        return http.build();
    }
}
