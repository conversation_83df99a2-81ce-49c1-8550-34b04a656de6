package com.ahss.uam.config;

import com.ahss.common.domain.context.UserContext;
import com.ahss.common.domain.context.UserContextHolder;
import com.ahss.uam.dm.enums.Authority;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationCredentialsNotFoundException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.filter.OncePerRequestFilter;

@Slf4j
@Component
@RequiredArgsConstructor
public class CustomJwtAuthenticationFilter extends OncePerRequestFilter {

  private static final String API_KEY_HEADER = "X-API-KEY";
  private static final String ADMIN_ROLE = "ADMIN";
  private static final String SYSTEM_USER = Authority.SYSTEM_USER.getAuthority();
  private static final String JWT_CLAIM_NAME_NAME = "name";
  private static final String JWT_CLAIM_NAME_EMAIL = "email";
  private static final String JWT_CLAIM_NAME_MEMBEROF = "memberOf";
  private static final String JWT_CLAIM_NAME_GROUPS = "groups";

    @Value("${ahpp.adgroup-pattern: (?<product>\\w+)_(?<tenant>\\w+)_(?<market>\\w+)_(?<authority>\\w+)}")
    private String adGroupPattern;

  private static final String[] SKIP_PATHS = {"/v3/api-docs", "/swagger-ui", "/actuator", "/public", "/error", "/v1/api/uam/public"};

  @Override
  protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
      throws IOException, ServletException {
    try {
      if (shouldSkipFilter(request)) {
        filterChain.doFilter(request, response);
        return;
      }

      var authentication = getJwtAuthentication();

      if (authentication != null && authentication.getToken() instanceof Jwt jwt) {
        processAuthentication(request, jwt, authentication);
      }

      filterChain.doFilter(request, response);
    } catch (AuthenticationCredentialsNotFoundException e) {
      log.debug("No authentication credentials found for path: {}", request.getRequestURI());
      filterChain.doFilter(request, response);
    } catch (Exception e) {
      log.error("Authentication processing error: {}", e.getMessage());
      response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Authentication failed");
    } finally {
      UserContextHolder.clear();
    }
  }

  private JwtAuthenticationToken getJwtAuthentication() {
    return (JwtAuthenticationToken) Optional
        .ofNullable(SecurityContextHolder
            .getContext()
            .getAuthentication())
        .filter(JwtAuthenticationToken.class::isInstance)
        .orElse(null);
  }

  private boolean shouldSkipFilter(HttpServletRequest request) {
    String path = request
        .getRequestURI()
        .toLowerCase();
    return path.equals("/") || path.equals("/error") || java.util.Arrays
        .stream(SKIP_PATHS)
        .anyMatch(skipPath -> path.contains(skipPath.toLowerCase()));
  }

  private void processAuthentication(HttpServletRequest request, Jwt jwt, JwtAuthenticationToken authentication) {
    try {
      List<GrantedAuthority> updatedAuthorities = new ArrayList<>(authentication.getAuthorities());
      UserContext userContext = createUserContext(request, jwt);
      updateSecurityContext(jwt, updatedAuthorities, userContext);
    } catch (Exception e) {
      log.error("Failed to process authentication: {}", e.getMessage());
      throw new AuthenticationCredentialsNotFoundException("Failed to process authentication", e);
    }
  }

  private UserContext createUserContext(HttpServletRequest request, Jwt jwt) {
    String apiKey = request.getHeader(API_KEY_HEADER);

    if (isValidApiKey(apiKey)) {
      return createSystemUserContext(jwt);
    }

    return createRegularUserContext(jwt);
  }

  private boolean isValidApiKey(String apiKey) {
    return apiKey != null && !apiKey.isEmpty();
  }

  private UserContext createSystemUserContext(Jwt jwt) {
    final String name = jwt.getClaimAsString(JWT_CLAIM_NAME_NAME);

      // Todo: temporarily manipulate AD Group value of Core Claims to make it adaptable with AD Group pattern.
      // After Core Claims team updated to correct AD Group name, revert code back to this version:
//      final List<String> memberOf = getClaimAsList(jwt, JWT_CLAIM_NAME_MEMBEROF);
      final List<String> memberOf = jwt
          .getClaimAsStringList("memberOf")
          .stream()
          .map(member -> member.equals("AH_AHCC_MEDICARD_USER_PREPROD") ? "AH_AHCC_MPI_PH_USER_PREPROD" : member)
          .toList();

    return UserContext
        .builder()
        .memberOf(memberOf)
        .sub(jwt.getSubject())
        .name(name)
        .accessToken(jwt.getTokenValue())
        .authorities(List.of(SYSTEM_USER))
        .build();
  }

  private UserContext createRegularUserContext(Jwt jwt) {
    String sub = jwt.getSubject();
    String email = jwt.getClaimAsString(JWT_CLAIM_NAME_EMAIL);
    String name = jwt.getClaimAsString(JWT_CLAIM_NAME_NAME);

      // Todo: temporarily manipulate AD Group value of Core Claims to make it adaptable with AD Group pattern.
      // After Core Claims team updated to correct AD Group name, revert code back to this version:
//      final List<String> memberOf = getClaimAsList(jwt, JWT_CLAIM_NAME_MEMBEROF);
    final List<String> memberOf = jwt
          .getClaimAsStringList("memberOf")
          .stream()
          .map(member -> member.equals("AH_AHCC_MEDICARD_USER_PREPROD") ? "AH_AHCC_MPI_PH_USER_PREPROD" : member)
          .toList();

    List<String> groups = getClaimAsList(jwt, JWT_CLAIM_NAME_GROUPS);

    // Combine memberOf and groups claims
    List<String> allGroups = new ArrayList<>();
    allGroups.addAll(memberOf);
    allGroups.addAll(groups);

      return UserContext
          .builder()
          .memberOf(allGroups)
          .email(email)
          .sub(sub)
          .name(name)
          .accessToken(jwt.getTokenValue())
          .build()
          .extractMemberOf(adGroupPattern);
  }

  @SuppressWarnings("unchecked")
  private List<String> getClaimAsList(Jwt jwt, String claimName) {
    Object claim = jwt.getClaim(claimName);
    if (claim == null) {
      return new ArrayList<>();
    }
    if (claim instanceof List) {
      return (List<String>) claim;
    }
    return new ArrayList<>();
  }

  private void updateSecurityContext(Jwt jwt, List<GrantedAuthority> authorities, UserContext userContext) {
    // Add roles based on user context
    if (SYSTEM_USER.equalsIgnoreCase(userContext.getSub())) {
      // Intentionally left empty
      // We can add/modify/customize authorities here
    }

      // Add roles based on memberOf groups if not yet handled in CustomJwtAuthenticationConverter
      // userContext.getMemberOf().forEach(group -> authorities.add(new SimpleGrantedAuthority(group)));
      // userContext.getMemberOf().forEach(group -> authorities.add(new SimpleGrantedAuthority("ROLE_" + group)));
      if (!CollectionUtils.isEmpty(userContext.getAuthorities())) {
          userContext
              .getAuthorities()
              .forEach(group -> authorities.add(new SimpleGrantedAuthority(group)));
          SecurityContextHolder
              .getContext()
              .setAuthentication(new JwtAuthenticationToken(jwt, authorities));
      }
    UserContextHolder.setUserContext(userContext);
  }
}
