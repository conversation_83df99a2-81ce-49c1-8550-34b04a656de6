package com.ahss.uam.config;

import com.nimbusds.jose.JOSEObjectType;
import com.nimbusds.jose.proc.BadJOSEException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.oauth2.core.DelegatingOAuth2TokenValidator;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.oauth2.core.OAuth2TokenValidator;
import org.springframework.security.oauth2.core.OAuth2TokenValidatorResult;
import org.springframework.security.oauth2.jwt.*;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Component
public class JwtDecoderFactory {

  public JwtDecoder getServerJwtDecoder(String secretKey) {
    SecretKey key = new SecretKeySpec(secretKey.getBytes(), "HmacSHA256");
    return NimbusJwtDecoder
        .withSecretKey(key)
        .jwtProcessorCustomizer(processor -> {
          Set<JOSEObjectType> types = new HashSet<>(List.of(JOSEObjectType.JWT));
          processor.setJWSTypeVerifier((type, context) -> {
            if (type != null && !types.contains(type)) {
              throw new BadJOSEException("JOSE header typ (type) " + type + " not allowed");
            }
          });
        })
        .build();
  }

  public JwtDecoder getClientJwtDecoder(String issuer, String jwkSetUri, String audience) {
    log.info("Issuer: {}", issuer);
    log.info("JwkSetUri: {}", jwkSetUri);
    NimbusJwtDecoder jwtDecoder = NimbusJwtDecoder
        .withJwkSetUri(jwkSetUri)
        .jwtProcessorCustomizer(processor -> {
          Set<JOSEObjectType> types = new HashSet<>(List.of(JOSEObjectType.JWT, new JOSEObjectType("at+jwt")));
          processor.setJWSTypeVerifier((type, context) -> {
            if (type != null && !types.contains(type)) {
              throw new BadJOSEException("JOSE header typ (type) " + type + " not allowed");
            }
          });
        })
        .build();

    OAuth2TokenValidator<Jwt> validator = new DelegatingOAuth2TokenValidator<>(
        JwtValidators.createDefaultWithIssuer(issuer),
        audienceValidator(audience),
        subjectValidator(),
        scopeValidator(),
        groupValidator()
    );

    jwtDecoder.setJwtValidator(validator);
    return jwtDecoder;
  }

  private OAuth2TokenValidator<Jwt> audienceValidator(String audience) {
    return new JwtClaimValidator<Collection<String>>(
        JwtClaimNames.AUD,
        aud -> {
          if (aud == null) {
            return false;
          }
          return aud.contains(audience);
        }
    );
  }

  private OAuth2TokenValidator<Jwt> subjectValidator() {
    return new JwtClaimValidator<String>(
        JwtClaimNames.SUB,
        sub -> sub != null && !sub
            .trim()
            .isEmpty()
    );
  }

  private OAuth2TokenValidator<Jwt> scopeValidator() {
    return jwt -> {
      List<String> scopes = jwt.getClaimAsStringList("scp");
      if (scopes == null || scopes.isEmpty()) {
        OAuth2Error error = new OAuth2Error(
            "invalid_scope",
            "The token has no scopes",
            null
        );
        return OAuth2TokenValidatorResult.failure(error);
      }
      return OAuth2TokenValidatorResult.success();
    };
  }

  private OAuth2TokenValidator<Jwt> groupValidator() {
    return jwt -> {
      List<String> groups = jwt.getClaimAsStringList("memberOf");
      if (groups == null || groups.isEmpty()) {
        OAuth2Error error = new OAuth2Error(
            "invalid_groups",
            "The token has no groups",
            null
        );
        return OAuth2TokenValidatorResult.failure(error);
      }
      return OAuth2TokenValidatorResult.success();
    };
  }
}
