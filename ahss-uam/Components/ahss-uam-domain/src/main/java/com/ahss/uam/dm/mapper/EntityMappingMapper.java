package com.ahss.uam.dm.mapper;

import com.ahss.uam.db.model.EntityMapping;
import com.ahss.uam.dm.dto.EntityMappingDTO;
import java.util.List;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface EntityMappingMapper {

    @Mapping(target = "userEntity", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    EntityMapping toEntity(EntityMappingDTO entityDTO);

    EntityMappingDTO toDto(EntityMapping entityMapping);

    List<EntityMapping> toEntities(List<EntityMappingDTO> entityDTO);

    List<EntityMappingDTO> toDtos(List<EntityMapping> entity);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    EntityMapping partialUpdate(EntityMappingDTO entityMappingDTO, @MappingTarget EntityMapping entityMapping);
}
