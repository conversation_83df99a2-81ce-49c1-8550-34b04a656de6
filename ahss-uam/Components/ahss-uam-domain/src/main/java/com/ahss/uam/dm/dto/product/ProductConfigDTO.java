package com.ahss.uam.dm.dto.product;

import com.ahss.uam.db.model.ValueFormatType;
import com.ahss.uam.dm.dto.product.enums.ProductConfigStatus;
import com.ahss.uam.dm.dto.product.enums.ProductConfigType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class ProductConfigDTO {

    private Integer productId;
    private String productCode;
    private ProductConfigType configType;
    private String configValue;
    private ValueFormatType configValueFormatType;
    private String regex;
    private ProductConfigStatus productConfigStatus;
}
