package com.ahss.uam.dm.dto.product;

import com.ahss.uam.dm.dto.product.enums.ProductStatus;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Data
public class ProductDTO {

    private Integer productId;
    private String productCode;
    private String productName;
    private String description;
    private ProductStatus productStatus;
    private List<ProductConfigDTO> productConfigs;
}
