package com.ahss.common.api;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;

import java.io.IOException;

public class ResponseCodeDeserializer extends JsonDeserializer<ResponseCode> {
  @Override
  public ResponseCode deserialize(JsonParser p, DeserializationContext ctx) throws IOException {
    JsonNode node = p.getCodec().readTree(p);
    return BaseResponseCode
        .builder()
        .code(node.get("code").asInt())
        .message(node.get("message").asText())
        .detailMessage(node.get("detailMessage").asText())
        .build();
  }
}