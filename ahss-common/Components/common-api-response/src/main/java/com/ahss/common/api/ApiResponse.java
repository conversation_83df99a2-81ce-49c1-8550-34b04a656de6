package com.ahss.common.api;

import static com.ahss.common.api.AHSSResponseCode.RC_200_000;
import static com.ahss.common.api.AHSSResponseCode.RC_500_000;
import static com.ahss.common.api.ResponseStatus.FAILED;
import static com.ahss.common.api.ResponseStatus.SUCCESS;

import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiResponse<T, R> {

  @Builder.Default
  private ResponseStatus status = FAILED;
  @Builder.Default
  private ResponseCode responseCode = RC_500_000;
  private T data;
  private R request;
  private Map<String, Object> actions;

  public static <T, V> ApiResponse<T, V> generateSuccessfulResponse() {
    return ApiResponse.<T, V>builder().responseCode(RC_200_000).status(SUCCESS).build();
  }

  public static <T, V> ApiResponse<T, V> generateFailedResponse() {
    return ApiResponse.<T, V>builder().responseCode(RC_500_000).status(FAILED).build();
  }
}