package com.ahss.common.api;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum AHSSResponseCode implements ResponseCode {

    // RC_<HTTP Response Code>_XX(code, shortMessage, detailMessage)
    RC_200_000(200000, "Success", "Request has been processed successfully"),
    // 400 Bad Request – This means that client-side input fails validation.
    RC_400_000(400000, "Bad Request", "Bad Request"),
    RC_400_001(400001, "Duplicate Record", "Duplicate PTC record found in PTC creation"),
    RC_400_002(400002, "Invalid Request", "Request parameters were invalid"),
    RC_400_003(400003, "Invalid Token", "Invalid card token"),
    RC_400_004(400004, "Duplicate Record", "Duplicate ProductCode found for other product Id"),
    RC_400_005(400005, "Duplicate Record", "TenantCode already existed"),
    RC_400_006(400006, "Invalid Request", "Organization id can not be same as organization's parent Id"),
    RC_400_007(400007, "Invalid Request", "Organization is required for BUSINESS_IN and BUSINESS_OUT tenant type "),
    RC_400_008(400008, "Invalid Request", "EntityMappingsId does not belong to User"),
    RC_400_009(400009, "Invalid Request", "Provided redirect URL is does not match with the registered Product Domain"),
    RC_400_010(400010, "Duplicate Record", "Client Id already existed"),
    RC_400_011(400011, "Invalid ClientId", "Request failed while encrypting the client Id"),
    //401 Unauthorized – This means the user isn’t not authorized to access a resource. It usually returns when the user isn’t authenticated.
    RC_401_000(401000, "Unauthorized", "Unauthorized"),
    RC_401_001(401001, "Unauthorized", "Specific unauthorized error"),
    //403 Forbidden – This means the user is authenticated, but it’s not allowed to access a resource.
    RC_403_000(403000, "Forbidden", "Forbidden"),
    RC_403_001(403001, "Forbidden", "You are not allow to access FS app"),
    //404 Not Found – This indicates that a resource is not found.
    RC_404_000(404000, "Not Found", "Not found"),
    RC_404_001(404001, "User Not Found", "User not found"),
    RC_404_002(404002, "Resource Not Found", "Resource not found"),
    RC_404_003(404003, "Tenant Not Found", "Tenant not found"),
    RC_404_004(404004, "Domain Not Found", "User email domain does not match any tenant"),
    RC_404_005(404005, "Token Not Found", "Access token not found for the given state"),
    RC_404_006(404006, "Product Not Found", "Product not found for the given product code"),
    //500 Internal server error – This is a generic server error. It probably shouldn’t be thrown explicitly.
    RC_500_000(500000, "Internal Server Error", "Internal server error"),
    RC_500_001(500001, "Internal Server Error", "Specific internal server error"),
    //502 Bad Gateway – This indicates an invalid response from an upstream server.
    RC_502_000(502000, "Bad Gateway", "Bad Gateway"),
    RC_502_001(502001, "Bad Gateway", "Specific bad gateway error"),
    //503 Service Unavailable – This indicates that something unexpected happened on server side (It can be anything like server overload, some
    // parts of the system failed, etc.).
    RC_503_000(503000, "Service Unavailable", "Service unavailable"),
    RC_503_001(503001, "Service Unavailable", "Specific Service unavailable error");

    private final Integer code;
    private final String message;
    private final String detailMessage;

    public static AHSSResponseCode fromCode(int code) {
        for (AHSSResponseCode AHSSResponseCode : AHSSResponseCode.values()) {
            if (code == AHSSResponseCode.getCode()) {
                return AHSSResponseCode;
            }
        }
        return null;
    }

    @Override
    public <T extends Enum<T>> T getEnum(Class<T> t) {
        return (T) this;
    }
}
