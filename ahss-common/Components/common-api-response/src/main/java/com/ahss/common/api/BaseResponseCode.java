package com.ahss.common.api;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class BaseResponseCode implements ResponseCode {
  private static final String CODE_PREFIX = "RC";
  private static final String CODE_DELIMITER = "_";
  private static final int CODE_GROUP_LENGTH = 3;

  private Integer code;
  private String message;
  private String detailMessage;

  @Override
  public <T extends Enum<T>> T getEnum(Class<T> enumClazz) {
    final String name = CODE_PREFIX + CODE_DELIMITER + String.valueOf(this.getCode()).substring(0, CODE_GROUP_LENGTH) + "_" +
                        String.valueOf(this.getCode()).substring(CODE_GROUP_LENGTH);
    return T.valueOf(enumClazz, name);
  }
}