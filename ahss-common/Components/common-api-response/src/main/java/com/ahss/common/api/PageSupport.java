package com.ahss.common.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Map;

public record PageSupport<T>(List<T> content, int page, int size, long totalElements) {

    public static final String FIRST_PAGE_NUM = "0";
    public static final String DEFAULT_PAGE_SIZE = "20";

    @JsonProperty
    public int totalPages() {
        return size > 0 ? (int) ((totalElements - 1) / size + 1) : 0;
    }

    public Map<String, Object> pageInfo() {
        return Map.of("totalPages", totalPages(), "totalElements", totalElements, "page", page, "size", size);
    }

}
