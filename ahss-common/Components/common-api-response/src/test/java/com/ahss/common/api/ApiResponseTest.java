package com.ahss.common.api;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

@Slf4j
class ApiResponseTest {

  @Test
  void apiResponseSerDeTest() {
    List<FormField> formFields = new ArrayList<>();
    formFields.add(new FormField("name", "Dennis Dao"));

    ApiResponse<RedirectInfo, CardRegisterSpec> initialPpiResponse = ApiResponse.generateFailedResponse();
    log.info(initialPpiResponse.toString());

    RedirectInfo redirectInfo = RedirectInfo.builder().url("http://localhost/secureWidget").method(HttpMethod.GET).formFields(formFields).build();

    CardRegisterSpec cardRegisterSpec = CardRegisterSpec
        .builder()
        .cardNum("****************")
        .expMonth("11")
        .expYear("2022")
        .cardToken("12341231312")
        .merchantId("0987654321")
        .clientId("1234567890")
        .signature("dkahdas8979sauoj219080fsuofsalkhkasuey790128psahfsdkvdsiovy89se")
        .build();

    ApiResponse<RedirectInfo, CardRegisterSpec> apiResponse =
        ApiResponse.<RedirectInfo, CardRegisterSpec>builder().status(ResponseStatus.SUCCESS).responseCode(
            AHSSResponseCode.RC_200_000).data(redirectInfo).request(
            cardRegisterSpec).build();

    CardRegisterSpec invalidCardRegisterSpec = CardRegisterSpec
        .builder()
        .cardNum("****************")
        .expMonth("11")
        .expYear("2022")
        .cardToken("999999999")
        .merchantId("0987654321")
        .clientId("1234567890")
        .signature("dkahdas8979sauoj219080fsuofsalkhkasuey790128psahfsdkvdsiovy89se")
        .build();

    ApiResponse<RedirectInfo, CardRegisterSpec> invalidApiResponse = ApiResponse
        .<RedirectInfo, CardRegisterSpec>builder()
        .status(ResponseStatus.FAILED)
        .responseCode(AHSSResponseCode.RC_400_003)
        .request(invalidCardRegisterSpec)
        .actions(Map.of("WARN", "Message"))
        .build();

    ObjectMapper objectMapper = new ObjectMapper();
    try {
      final String json = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(apiResponse);
      log.info(json);

      var typeReference = new TypeReference<ApiResponse<RedirectInfo, CardRegisterSpec>>() {
      };

      ApiResponse<RedirectInfo, CardRegisterSpec> deserializedObject = objectMapper.readValue(json, typeReference);
      log.info(deserializedObject.toString());

      log.info("---------- Invalid Request ----------");
      final String invalidJson = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(invalidApiResponse);
      log.info(invalidJson);
      ApiResponse<RedirectInfo, CardRegisterSpec> invalidDeserializedObject = objectMapper.readValue(invalidJson, typeReference);
      log.info(invalidDeserializedObject.toString());
      log.info(invalidDeserializedObject.getResponseCode().getEnum(AHSSResponseCode.class).name());
      log.info(Objects.requireNonNull(AHSSResponseCode.fromCode(invalidDeserializedObject.getResponseCode().getCode())).name());
      Assertions.assertEquals(AHSSResponseCode.RC_400_003, AHSSResponseCode.fromCode(invalidDeserializedObject.getResponseCode().getCode()));
    } catch (IOException e) {
      log.error(e.getMessage(), e);
    }
  }

  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class RedirectInfo {

    private String url;
    private HttpMethod method;
    private List<FormField> formFields;

  }

  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class CardRegisterSpec {

    private String expMonth;
    private String expYear;
    private String cardNum;
    private String cardToken;
    private String clientId;
    private String merchantId;
    private String signature;
  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class FormField {

    private String fieldName;
    private String value;
  }

  enum HttpMethod {
    GET, POST, PUT, DELETE
  }
}