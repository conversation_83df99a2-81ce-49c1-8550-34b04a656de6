package com.ahss.common.utils.masking;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SensitiveDataMasker {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static Map<String, Object> maskSensitiveData(final Object object) {
        Map<String, Object> result = new HashMap<>();
        try {
            if (object instanceof Map) {
                for (Map.Entry<?, ?> entry : ((Map<?, ?>) object).entrySet()) {
                    String key = (String) entry.getKey();
                    Object value = entry.getValue();

                    if (value instanceof Map) {
                        result.put(key, maskSensitiveData(value));
                    } else if (value instanceof List) {
                        result.put(key, processList((List<?>) value));
                    } else {
                        result.put(key, value);
                    }
                }
                return result;
            }

            Field[] fields = object
                .getClass()
                .getDeclaredFields();

            for (Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(object);

                if (field.isAnnotationPresent(SensitiveData.class) && value instanceof String) {
                    SensitiveData annotation = field.getAnnotation(SensitiveData.class);
                    int maskLength = annotation.maskLength();
                    result.put(field.getName(), maskValue((String) value, maskLength));
                } else if (value instanceof Map) {
                    result.put(field.getName(), maskSensitiveData(value));
                } else if (value instanceof List) {
                    result.put(field.getName(), processList((List<?>) value));
                } else if (value != null && isCustomObject(value)) {
                    result.put(field.getName(), maskSensitiveData(value));
                } else {
                    result.put(field.getName(), value);
                }
            }
        } catch (IllegalAccessException e) {
            throw new RuntimeException("Failed to mask sensitive data", e);
        }
        return result;
    }

    private static List<Object> processList(final List<?> list) {
        return list
            .stream()
            .map(item -> {
                if (item instanceof Map) {
                    return maskSensitiveData(item);
                } else if (isCustomObject(item)) {
                    return maskSensitiveData(item);
                } else {
                    return item;
                }
            })
            .toList();
    }


    private static boolean isCustomObject(final Object value) {
        return !(value instanceof String) &&
            !(value instanceof Number) &&
            !(value instanceof Boolean) &&
            !(value instanceof List) &&
            !(value instanceof Map);
    }

    private static String maskValue(String value, int maskLength) {
        if (value == null) {
            return null;
        }
        if (value.length() <= maskLength) {
            return "*".repeat(value.length());
        }
        return "*".repeat(maskLength) + value.substring(maskLength);
    }
}
