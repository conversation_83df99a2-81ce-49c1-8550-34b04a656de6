package com.ahss.gateway.core.service.impl;

import com.ahss.gateway.core.service.CommonService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class CommonServiceImplTest {

  private CommonService commonService;
  @BeforeEach
  void setUp() {
    commonService = new CommonServiceImpl();
  }

  @Test
  void sayHello() {
    Assertions.assertEquals("hello world", commonService.sayHello());
  }
}
