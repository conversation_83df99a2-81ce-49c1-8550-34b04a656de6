server:
  port: 8888

spring:
  application:
    name: ahss-gateway
  main:
    web-application-type: reactive
  reactor:
    context-propagation: auto
  web:
    cors:
      allowed-origins: https://wonderful-meadow-0b4e55c00.5.azurestaticapps.net, https://dev.app.ahpp.amplifyhealth.com
      allowed-methods: GET, POST, PUT, DELETE, OPTIONS
      allowed-headers: "*"
      allow-credentials: true

management:
  tracing:
    sampling:
      probability: 0.0
  zipkin:
    tracing:
      endpoint: ${METRICS_COLLECTOR_ENDPOINT:}
  endpoints:
    web:
      exposure:
        include: prometheus,health
    #        include: "*"
  endpoint:
    health:
      show-details: always
  metrics:
    distribution:
      percentiles-histogram:
        http:
          server:
            requests: false
    enable:
      all: false

ahss:
  ahpp:
    path: ${AHPP_PATH:/ahss-pp/**}
    stripPrefix: 1
    uri:
      scheme: ${AHPP_SCHEME:http}
      host: ${AHPP_HOST:ahss-pp-web}
      port: ${AHPP_PORT:8001}
  uam:
    path: ${UAM_PATH:/ahss-uam/**}
    stripPrefix: 1
    uri:
      scheme: ${UAM_SCHEME:http}
      host: ${UAM_HOST:ahss-uam-web}
      port: ${UAM_PORT:8011}
