controllers:
  main:
    strategy: Recreate
    type: deployment
    replicas: 1
    containers:
      main:
        image:
          repository: acrah01seapahss01.azurecr.io/ahss/ahss-pp-web
          tag: latest
          pullPolicy: Always
        envFrom:
          - secret: secret
          - secretRef:
              name: ahss-database-credentials
service:
  main:
    controller: main
    ports:
      http:
        port: 8001
        protocol: HTTP

ingress:
  main:
    enabled: true
    className: azure-application-gateway
    annotations:
      appgw.ingress.kubernetes.io/backend-path-prefix: "/"
      appgw.ingress.kubernetes.io/use-private-ip: "true"
      appgw.ingress.kubernetes.io/health-probe-port: "8001"
      appgw.ingress.kubernetes.io/health-probe-path: "/actuator/health"
    hosts:
      - host: api.ahpp.amplifyhealth.com
        paths:
          - path: /ahss-pp/*
            pathType: Prefix
            service:
              identifier: main
    tls:
      - hosts:
          - api.ahpp.amplifyhealth.com
        secretName: apiahpp-tls
secrets:
  secret:
    enabled: true
    stringData:
      DB_URL: "**************************************************************************"
      JWK_SET_URI_WORKFORCE: "https://aia.okta.com/oauth2/auso3tx5fwrMSxMqO4x7/v1/keys"
      ISSUER_URI_WORKFORCE: "https://aia.okta.com/oauth2/auso3tx5fwrMSxMqO4x7"
      JWT_AUDIENCE_WORKFORCE: "Amplify Health Product Portal"
      JWK_SET_URI_CIAM: "https://ah-prod.okta.com/oauth2/aus6g65022nDV0a373l7/v1/keys"
      ISSUER_URI_CIAM: "https://ah-prod.okta.com/oauth2/aus6g65022nDV0a373l7"
      JWT_AUDIENCE_CIAM: "ah_server"
      PASSWORDLESS_ENABLED: "false"
      ENCRYPTION_SECRET_KEY: "vWYdLib9N8N0/BohltfEsrB207X33NUgJxO71NCrjeM="
      SWAGGER_SHOW_HIDDEN: "true"
      OKTA_API_GATEWAY_CLIENT_ID: "0oao3tudxdZGOq0MW4x7"
      APPLICATIONINSIGHTS_CONNECTION_STRING: "InstrumentationKey=499ef8dc-0b83-425d-b0fb-bf4ccead9ea8;IngestionEndpoint=https://southeastasia-1.in.applicationinsights.azure.com/;LiveEndpoint=https://southeastasia.livediagnostics.monitor.azure.com/;ApplicationId=b67484e0-b82d-4de7-80dc-bd011e67d8d3"
      SPRING_PROFILES_ACTIVE: "prod"
