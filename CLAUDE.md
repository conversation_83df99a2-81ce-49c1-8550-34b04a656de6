# Amplify Health Shared Services - Codebase Analysis

## Project Overview
Amplify Health Shared Services (AHSS) is a multi-module Spring Boot microservices project using Java 21 and Maven. It consists of three main services: Gateway, Product Portal, and User Access Management (UAM).

## Architecture & Technology Stack

### Core Technologies
- **Java 21** (minimum Java 17 supported)
- **Spring Boot 3.3.2** with Spring Cloud 2023.0.3
- **Spring Modulith 1.2.1** for modular architecture
- **Maven** for build and dependency management
- **PostgreSQL 15.7** database
- **Docker** with Google Jib plugin for containerization
- **Flyway** for database migrations
- **JOOQ** for type-safe SQL queries
- **Application Insights** for monitoring

### Project Structure
```
ah-shared-services/
├── ahss-common/           # Shared components
├── ahss-gateway/          # API Gateway service
├── ahss-product-portal/   # Product Portal service  
├── ahss-uam/             # User Access Management service
├── docker/               # Docker compose files
├── helm/                 # Kubernetes Helm charts
├── scripts/              # Build and utility scripts
└── documentation/        # Project documentation
```

### Services Architecture

#### 1. AHSS Gateway (Port 8888)
- **Entry Point**: `com.ahss.gateway.AHSSGatewayWebApplication`
- **Type**: Reactive Spring WebFlux gateway
- **Purpose**: Routes requests to Product Portal and UAM services
- **Key Features**:
  - CORS configuration for frontend applications
  - Request routing with path stripping
  - Observability with Zipkin/Jaeger tracing

#### 2. Product Portal (Port 8001)
- **Entry Point**: `com.ahss.pp.AHProductPortalApplication`
- **Type**: Traditional Spring MVC application
- **Purpose**: Product management and configuration
- **Key Features**:
  - Async processing enabled
  - JPA entity scanning
  - Database integration with Flyway migrations

#### 3. User Access Management - UAM (Port 8011)
- **Entry Point**: `com.ahss.uam.AHUamSpringApplication`
- **Type**: Traditional Spring MVC application
- **Purpose**: User authentication, authorization, and tenant management
- **Key Features**:
  - Multi-tenant architecture
  - Integration with external auth providers
  - HMAC-based security features

## Module Organization
Each service follows a consistent layered architecture:
- **Interface/**: Web layer (Controllers, REST APIs)
- **Components/**: Business logic layers
  - `*-common/`: Shared utilities
  - `*-config/`: Configuration classes
  - `*-core/`: Business logic and services
  - `*-db/`: Data access layer with repositories
  - `*-domain/`: Domain models and mappers

## Database & Data Access

### Database Setup
- **Database**: PostgreSQL 15.7
- **Connection**: `*****************************************`
- **Credentials**: username=`ahss`, password=`password`
- **Docker**: Available via `docker/all-in-one.yml`

### Data Access Technologies
- **Flyway**: Database migration management
- **JOOQ**: Type-safe SQL query generation
- **JPA/Hibernate**: Entity management and repositories

### Migration Commands
```bash
# Product Portal
mvn flyway:migrate -pl ahss-product-portal/Components/ahss-pp-db

# UAM
mvn flyway:migrate -pl ahss-uam/Components/ahss-uam-db
```

### JOOQ Code Generation
```bash
# Product Portal
mvn generate-sources -P generate-jooq -pl ahss-product-portal/Components/ahss-pp-db -amd

# UAM  
mvn generate-sources -P generate-jooq -pl ahss-uam/Components/ahss-uam-db -amd
```

## Build & Development

### Build Commands
```bash
# Full build with integration tests
mvn clean install -D spring.profiles.active=integration

# Build specific modules
mvn clean install -pl ahss-gateway,ahss-product-portal -amd

# Build with cache
mvn clean install -D spring.profiles.active=integration -D maven.build.cache.enabled=true

# Generate test reports
mvn clean install surefire-report:report -D aggregate=true
```

### Spring Profiles
- `default`: Default development profile (`application.yml`)
- `dev`: Development environment (`application-dev.yml`)
- `sit`: System Integration Testing (`application-sit.yml`)
- `uat`: User Acceptance Testing (`application-uat.yml`)
- `prod`: Production environment (`application-prod.yml`)
- `integration`: Integration testing profile (required for pre-push)

### Docker Development

#### Individual Service Build & Run
```bash
# Gateway
mvn clean install -pl ahss-gateway -amd -P local-docker -D spring.profiles.active=integration
mvn -pl ahss-gateway/Interface/ahss-gateway-web compile jib:dockerBuild -P local-docker
docker run --name ahss-gateway-web -p 8888:8888 ahss-gateway-web:0.0.1-SNAPSHOT

# Product Portal  
mvn clean install -pl ahss-product-portal -amd -P local-docker -D spring.profiles.active=integration
mvn -pl ahss-product-portal/Interface/ahss-pp-web compile jib:dockerBuild -P local-docker
docker run -e "DB_URL=****************************************************" \
  -e "DB_USERNAME=ahss" -e "DB_PASSWORD=password" \
  -p 8001:8001 --name ahss-pp-web ahss-pp-web:0.0.1-SNAPSHOT

# UAM
mvn clean install -pl ahss-uam -amd -P local-docker -D spring.profiles.active=integration
mvn -pl ahss-uam/Interface/ahss-uam-web compile jib:dockerBuild -P local-docker
docker run -e "DB_URL=****************************************************" \
  -e "DB_USERNAME=ahss" -e "DB_PASSWORD=password" \
  -p 8011:8011 --name ahss-uam-web ahss-uam-web:0.0.1-SNAPSHOT
```

#### All-in-One Docker Compose
```bash
# Start all services
docker compose -f ./docker/all-in-one.yml up

# Stop all services  
docker compose -f ./docker/all-in-one.yml down
```

#### Automated Build & Run Script
```bash
# Usage
./scripts/run-all.sh -h

# Example: Build and run with integration profile
./scripts/run-all.sh -p integration -m local-docker
```

## Testing Strategy

### Test Framework & Coverage
- **Framework**: JUnit 5 with Spring Boot Test
- **Test Count**: 235+ test methods across 45+ test files
- **Coverage**: JaCoCo Maven plugin enabled
- **Integration Tests**: 7 Spring Boot test classes for full integration testing

### Test Types
1. **Unit Tests**: Service layer, mappers, utilities
2. **Integration Tests**: Full application context with test containers
3. **Controller Tests**: Web layer testing with MockMvc
4. **Repository Tests**: Data access layer testing

### Test Execution
```bash
# Run all tests
mvn test

# Run with specific profile
mvn test -D spring.profiles.active=integration

# Generate test reports
mvn surefire-report:report -D aggregate=true
```

### Test Configuration
- **Test Containers**: PostgreSQL containers for integration tests
- **Test Profiles**: Separate `application-integration.yml` configurations
- **Base Test Classes**: `BaseControllerTest` for common test setup

## Quality & Security

### Pre-commit Hooks
```bash
# Install pre-push hook
cp ./scripts/pre-push ./.git/hooks/
```

**Pre-push Hook Features:**
- Runs full Maven build with integration tests
- Prevents commits with "WIP" messages
- Ensures code quality before push

### Security Features
- **Application Insights**: Monitoring and telemetry
- **CORS Configuration**: Configured for specific frontend domains
- **HMAC Services**: Cryptographic utilities in UAM
- **JWT Processing**: Token handling capabilities

### Monitoring & Observability
- **Zipkin/Jaeger**: Distributed tracing (port 16686)
- **Prometheus**: Metrics collection endpoints
- **Health Checks**: Spring Actuator endpoints
- **Application Insights**: Azure monitoring integration

## Development Guidelines

### Key Commands to Remember
```bash
# Build and test before push
mvn clean install -D spring.profiles.active=integration

# Start local database
docker compose -f ./docker/postgres-compose.yml up -d

# Run full development environment
./scripts/run-all.sh -p integration -m local-docker

# Generate database code
mvn generate-sources -P generate-jooq -pl ahss-*-portal/Components/ahss-*-db -amd
```

### Important Files
- `pom.xml`: Root Maven configuration
- `scripts/pre-push`: Git hook for quality gates
- `docker/all-in-one.yml`: Complete development environment
- `scripts/run-all.sh`: Automated build and deployment script

## Build & Deployment Pipeline

### Build Process Overview

#### 1. Maven Multi-Module Build System
The project uses a coordinated Maven multi-module build with the parent POM managing all services:

```bash
# Full build with integration tests (mandatory before push)
mvn clean install -D spring.profiles.active=integration

# Build specific modules with their dependencies
mvn clean install -pl ahss-gateway,ahss-product-portal -amd

# Resume build from failed module
mvn clean install -rf :ahss-pp-web -D spring.profiles.active=integration
```

#### 2. Containerization with Google Jib Plugin
**No Dockerfile Required** - Uses Google Jib Maven plugin for optimal Docker image creation:

```bash
# Build Docker images for each service
mvn -pl ahss-gateway/Interface/ahss-gateway-web compile jib:dockerBuild -P local-docker
mvn -pl ahss-product-portal/Interface/ahss-pp-web compile jib:dockerBuild -P local-docker  
mvn -pl ahss-uam/Interface/ahss-uam-web compile jib:dockerBuild -P local-docker
```

**Generated Docker Images:**
- `ahss-gateway-web:0.0.1-SNAPSHOT` (port 8888)
- `ahss-pp-web:0.0.1-SNAPSHOT` (port 8001)
- `ahss-uam-web:0.0.1-SNAPSHOT` (port 8011)

**Jib Plugin Benefits:**
- Faster builds with layer caching
- No Docker daemon required during build
- Optimized layer structure for better performance
- Reproducible builds

#### 3. Automated Build & Deployment Script
The `scripts/run-all.sh` script provides one-command build and deployment:

```bash
# Usage with options
./scripts/run-all.sh [-p|--profile <spring-profile>] [-m|--maven-profile <maven-profile>] [-d|--docker-compose <compose-file>]

# Example: Full integration build and local deployment
./scripts/run-all.sh -p integration -m local-docker
```

**Script Workflow:**
1. Compiles entire project with integration tests
2. Generates Surefire test reports
3. Builds Docker images for all three services
4. Launches complete stack via Docker Compose

### Deployment Strategies

#### 1. Local Development Environment
**Complete Stack Deployment:**
```bash
# Start all services with dependencies
docker compose -f ./docker/all-in-one.yml up

# Available services:
# - PostgreSQL database (port 5432)
# - AHSS Gateway (port 8888) 
# - Product Portal (port 8001)
# - UAM Service (port 8011)
# - Jaeger tracing UI (port 16686)
```

**Individual Service Deployment:**
```bash
# Database only
docker compose -f ./docker/postgres-compose.yml up -d

# Individual service with environment variables
docker run -e "DB_URL=****************************************************" \
  -e "DB_USERNAME=ahss" -e "DB_PASSWORD=password" \
  -e "METRICS_COLLECTOR_ENDPOINT=http://localhost:9411/api/v2/spans" \
  -p 8001:8001 --name ahss-pp-web ahss-pp-web:0.0.1-SNAPSHOT
```

#### 2. Azure Container Registry (ACR) Deployment
```bash
# Build and push to Azure Container Registry
mvn -pl ahss-gateway/Interface/ahss-gateway-web compile jib:dockerBuild -P azure-acr
mvn -pl ahss-product-portal/Interface/ahss-pp-web compile jib:dockerBuild -P azure-acr
mvn -pl ahss-uam/Interface/ahss-uam-web compile jib:dockerBuild -P azure-acr
```

#### 3. Kubernetes Deployment with Helm
**Helm Chart Structure:**
```
helm/
├── ahss-gateway-web/     # Gateway service Helm chart
├── ahss-pp-web/         # Product Portal Helm chart
└── ahss-uam-web/        # UAM service Helm chart
```

**Environment-Specific Deployment:**
- `values-dev.yaml` - Development environment
- `values-sit.yaml` - System Integration Testing
- `values-uat.yaml` - User Acceptance Testing  
- `values-prod.yaml` - Production environment

```bash
# Example Helm deployment
helm install ahss-gateway ./helm/ahss-gateway-web -f ./helm/ahss-gateway-web/values-dev.yaml
```

### Quality Gates & CI/CD Pipeline

#### Pre-Push Quality Gate
**Git Hook Location:** `scripts/pre-push`

**Quality Gate Process:**
1. **Integration Test Execution**: `mvn clean install -D spring.profiles.active=integration`
2. **WIP Commit Blocker**: Prevents commits with "WIP" in message
3. **Build Success Validation**: Exit code validation before push proceeds

```bash
# Install quality gate
cp ./scripts/pre-push ./.git/hooks/
chmod +x ./.git/hooks/pre-push
```

#### Build Profiles & Environment Configuration

**Maven Build Profiles:**
- `local-docker`: Local Docker image building
- `azure-acr`: Azure Container Registry deployment
- `no-tests`: Skip tests for faster builds
- `generate-jooq`: Database code generation

**Spring Runtime Profiles:**
- `default`: Local development with in-memory configurations
- `integration`: Integration testing with TestContainers
- `dev/sit/uat/prod`: Environment-specific configurations

### Database Deployment Strategy

#### Migration-First Approach
```bash
# Automatic migrations at application startup
# Manual migration execution:
mvn flyway:migrate -pl ahss-product-portal/Components/ahss-pp-db
mvn flyway:migrate -pl ahss-uam/Components/ahss-uam-db
```

#### Code-First Database Integration
```bash
# JOOQ code generation from live database schema
mvn generate-sources -P generate-jooq -pl ahss-product-portal/Components/ahss-pp-db -amd
mvn generate-sources -P generate-jooq -pl ahss-uam/Components/ahss-uam-db -amd
```

**Database Deployment Benefits:**
- Schema versioning with Flyway
- Type-safe queries with JOOQ
- Automatic code generation from schema changes
- TestContainers for isolated integration testing

### Monitoring & Observability in Deployment

#### Built-in Observability Stack
- **Distributed Tracing**: Zipkin/Jaeger integration with OpenTelemetry
- **Metrics Collection**: Prometheus endpoints on all services
- **Health Monitoring**: Spring Actuator health checks
- **Application Performance**: Azure Application Insights integration

#### Configuration Examples
```yaml
# application.yml - Observability configuration
management:
  tracing:
    sampling:
      probability: 0.0
  zipkin:
    tracing:
      endpoint: ${METRICS_COLLECTOR_ENDPOINT:}
  endpoints:
    web:
      exposure:
        include: prometheus,health
```

### Deployment Architecture Summary

**Key Deployment Features:**
1. **Zero-Dockerfile Strategy**: Jib plugin eliminates Dockerfile maintenance
2. **Environment Parity**: Same artifacts deployed across all environments
3. **Quality-First**: Mandatory integration tests before any deployment
4. **Database-Driven**: Schema migrations and code generation workflow
5. **Observability Built-in**: Monitoring and tracing configured by default
6. **Multi-Environment**: Helm charts with environment-specific configurations

**Deployment Dependencies:**
- PostgreSQL 15.7 database
- Jaeger/Zipkin for distributed tracing
- Azure Application Insights for monitoring
- Kubernetes cluster (for production deployments)

## Azure Application Gateway Ingress Controller (AGIC) Configuration

### Current AGIC Setup

The project uses **Azure Application Gateway Ingress Controller** for managing external traffic routing to the microservices. AGIC automatically configures Azure Application Gateway based on Kubernetes Ingress resources.

#### Existing Configuration Pattern
```yaml
ingress:
  main:
    enabled: true
    className: azure-application-gateway
    annotations:
      appgw.ingress.kubernetes.io/backend-path-prefix: "/"
      appgw.ingress.kubernetes.io/use-private-ip: "true"
      appgw.ingress.kubernetes.io/health-probe-port: "8888"
      appgw.ingress.kubernetes.io/health-probe-path: "/actuator/health"
    hosts:
      - host: api.ahpp.amplifyhealth.com
        paths:
          - path: /ahss-gateway/*
            pathType: Prefix
            service:
              identifier: main
    tls:
      - hosts:
          - api.ahpp.amplifyhealth.com
        secretName: apiahpp-tls
```

#### Current Domain Structure
- **Production**: `api.ahpp.amplifyhealth.com` with `apiahpp-tls` secret
- **UAT**: `uat.api.ahpp.amplifyhealth.com` with `uatapiahpp-tls` secret
- **Services**: Gateway (8888), Product Portal (8001), UAM (8011) each with separate ingress configurations

#### AGIC Annotations Used
- `appgw.ingress.kubernetes.io/backend-path-prefix`: Path rewriting for backend services
- `appgw.ingress.kubernetes.io/use-private-ip`: Internal traffic routing
- `appgw.ingress.kubernetes.io/health-probe-port`: Service health check port
- `appgw.ingress.kubernetes.io/health-probe-path`: Health endpoint path

### Adding Multiple Domain Listeners with TLS Secrets

#### 1. Multiple Domains in Single Ingress Resource

Configure multiple domains with individual TLS certificates:

```yaml
ingress:
  main:
    enabled: true
    className: azure-application-gateway
    annotations:
      appgw.ingress.kubernetes.io/backend-path-prefix: "/"
      appgw.ingress.kubernetes.io/use-private-ip: "true"
      appgw.ingress.kubernetes.io/health-probe-port: "8888"
      appgw.ingress.kubernetes.io/health-probe-path: "/actuator/health"
      appgw.ingress.kubernetes.io/ssl-redirect: "true"
      appgw.ingress.kubernetes.io/cookie-based-affinity: "Enabled"
    hosts:
      # Primary domain
      - host: api.ahpp.amplifyhealth.com
        paths:
          - path: /ahss-gateway/*
            pathType: Prefix
            service:
              identifier: main
      # Additional domain #1
      - host: gateway.amplifyhealth.com
        paths:
          - path: /ahss-gateway/*
            pathType: Prefix
            service:
              identifier: main
      # Additional domain #2  
      - host: api-v2.amplifyhealth.com
        paths:
          - path: /v2/ahss-gateway/*
            pathType: Prefix
            service:
              identifier: main
    tls:
      # TLS for primary domain
      - hosts:
          - api.ahpp.amplifyhealth.com
        secretName: apiahpp-tls
      # TLS for additional domain #1
      - hosts:
          - gateway.amplifyhealth.com
        secretName: gateway-amplifyhealth-tls
      # TLS for additional domain #2
      - hosts:
          - api-v2.amplifyhealth.com
        secretName: api-v2-amplifyhealth-tls
```

#### 2. Separate Ingress Resources for Different Domains

Alternative approach using multiple ingress resources:

```yaml
ingress:
  # Primary ingress
  main:
    enabled: true
    className: azure-application-gateway
    annotations:
      appgw.ingress.kubernetes.io/backend-path-prefix: "/"
      appgw.ingress.kubernetes.io/use-private-ip: "true"
      appgw.ingress.kubernetes.io/health-probe-port: "8888"
      appgw.ingress.kubernetes.io/health-probe-path: "/actuator/health"
    hosts:
      - host: api.ahpp.amplifyhealth.com
        paths:
          - path: /ahss-gateway/*
            pathType: Prefix
            service:
              identifier: main
    tls:
      - hosts:
          - api.ahpp.amplifyhealth.com
        secretName: apiahpp-tls

  # Secondary domain ingress
  secondary:
    enabled: true
    className: azure-application-gateway
    annotations:
      appgw.ingress.kubernetes.io/backend-path-prefix: "/"
      appgw.ingress.kubernetes.io/use-private-ip: "true"
      appgw.ingress.kubernetes.io/health-probe-port: "8888"
      appgw.ingress.kubernetes.io/health-probe-path: "/actuator/health"
    hosts:
      - host: gateway.amplifyhealth.com
        paths:
          - path: /api/*
            pathType: Prefix
            service:
              identifier: main
    tls:
      - hosts:
          - gateway.amplifyhealth.com
        secretName: gateway-amplifyhealth-tls

  # Third domain ingress
  v2api:
    enabled: true
    className: azure-application-gateway
    annotations:
      appgw.ingress.kubernetes.io/backend-path-prefix: "/"
      appgw.ingress.kubernetes.io/use-private-ip: "true"
      appgw.ingress.kubernetes.io/health-probe-port: "8888"
      appgw.ingress.kubernetes.io/health-probe-path: "/actuator/health"
    hosts:
      - host: api-v2.amplifyhealth.com
        paths:
          - path: /v2/*
            pathType: Prefix
            service:
              identifier: main
    tls:
      - hosts:
          - api-v2.amplifyhealth.com
        secretName: api-v2-amplifyhealth-tls
```

#### 3. TLS Certificate Management

**Creating TLS Secrets:**
```bash
# Create TLS secrets for each domain
kubectl create secret tls gateway-amplifyhealth-tls \
  --cert=gateway.amplifyhealth.com.crt \
  --key=gateway.amplifyhealth.com.key \
  --namespace=your-namespace

kubectl create secret tls api-v2-amplifyhealth-tls \
  --cert=api-v2.amplifyhealth.com.crt \
  --key=api-v2.amplifyhealth.com.key \
  --namespace=your-namespace
```

**Using cert-manager for Automatic Certificate Management:**
```yaml
# Add to your values.yaml
certManager:
  enabled: true
  certificates:
    - name: gateway-amplifyhealth-tls
      dnsNames:
        - gateway.amplifyhealth.com
      issuer: letsencrypt-prod
    - name: api-v2-amplifyhealth-tls
      dnsNames:
        - api-v2.amplifyhealth.com
      issuer: letsencrypt-prod
```

#### 4. Advanced AGIC Annotations

```yaml
annotations:
  # Backend path modifications
  appgw.ingress.kubernetes.io/backend-path-prefix: "/"
  
  # Use private IP for internal traffic
  appgw.ingress.kubernetes.io/use-private-ip: "true"
  
  # Health probes configuration
  appgw.ingress.kubernetes.io/health-probe-port: "8888"
  appgw.ingress.kubernetes.io/health-probe-path: "/actuator/health"
  appgw.ingress.kubernetes.io/health-probe-interval: "30"
  appgw.ingress.kubernetes.io/health-probe-timeout: "5"
  appgw.ingress.kubernetes.io/health-probe-unhealthy-threshold: "3"
  
  # SSL configuration
  appgw.ingress.kubernetes.io/ssl-redirect: "true"
  appgw.ingress.kubernetes.io/appgw-ssl-certificate: "your-ssl-cert-name"
  
  # Request routing
  appgw.ingress.kubernetes.io/request-timeout: "60"
  appgw.ingress.kubernetes.io/connection-draining: "true"
  appgw.ingress.kubernetes.io/connection-draining-timeout: "30"
  
  # Session affinity
  appgw.ingress.kubernetes.io/cookie-based-affinity: "Enabled"
  appgw.ingress.kubernetes.io/cookie-based-affinity-distinct-name: "ApplicationGatewayAffinity"
  
  # WAF configuration (if enabled)
  appgw.ingress.kubernetes.io/waf-policy-for-path: "/subscriptions/.../wafPolicies/your-waf-policy"
```

#### 5. Environment-Specific Multi-Domain Configuration

**Example values-prod.yaml with multiple domains:**
```yaml
ingress:
  main:
    enabled: true
    className: azure-application-gateway
    annotations:
      appgw.ingress.kubernetes.io/backend-path-prefix: "/"
      appgw.ingress.kubernetes.io/use-private-ip: "true"
      appgw.ingress.kubernetes.io/health-probe-port: "8888"
      appgw.ingress.kubernetes.io/health-probe-path: "/actuator/health"
      appgw.ingress.kubernetes.io/ssl-redirect: "true"
    hosts:
      - host: api.ahpp.amplifyhealth.com
        paths:
          - path: /ahss-gateway/*
            pathType: Prefix
      - host: gateway.amplifyhealth.com  
        paths:
          - path: /api/*
            pathType: Prefix
      - host: secure-api.amplifyhealth.com
        paths:
          - path: /secure/*
            pathType: Prefix
    tls:
      - hosts:
          - api.ahpp.amplifyhealth.com
        secretName: apiahpp-tls
      - hosts:
          - gateway.amplifyhealth.com
        secretName: gateway-tls
      - hosts:
          - secure-api.amplifyhealth.com
        secretName: secure-api-tls
```

#### 6. AGIC Deployment Commands

```bash
# Deploy with multiple domains
helm upgrade --install ahss-gateway \
  ./helm/ahss-gateway-web \
  -f ./helm/ahss-gateway-web/values-prod.yaml \
  --namespace your-namespace

# Verify ingress configuration
kubectl get ingress -n your-namespace
kubectl describe ingress ahss-gateway-main -n your-namespace

# Check TLS secrets
kubectl get secrets -n your-namespace | grep tls

# Monitor AGIC controller logs
kubectl logs -n agic-system deployment/ingress-azure
```

#### Benefits of AGIC Multi-Domain Configuration

1. **Multiple Domain Support**: Route traffic from different domains to the same service
2. **Individual TLS Certificates**: Each domain can have its own SSL certificate
3. **Path-based Routing**: Different paths for different domains
4. **Environment Flexibility**: Different domain configurations per environment
5. **Health Monitoring**: Application Gateway health probes for each configuration
6. **Automatic Gateway Configuration**: AGIC automatically configures Azure Application Gateway
7. **SSL Termination**: Handle SSL termination at the gateway level
8. **Session Affinity**: Cookie-based session persistence when needed
9. **WAF Integration**: Web Application Firewall policies per domain/path